/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333333;
    background-color: #f8f9fa;
    -webkit-app-region: no-drag; /* Ensure body doesn't interfere with dragging */
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
    color: white;
    padding: 1.5rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

/* Main Navigation */
.main-nav {
    display: flex;
    gap: 0.5rem;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.nav-tab.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-image {
    height: 60px;
    width: auto;
    max-width: 80px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    object-fit: contain; /* Prevents logo squishing */
    filter: brightness(0) invert(1); /* Makes logo white */
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-icon {
    font-size: 2.5rem;
    color: #327881;
}

.logo-section h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    color: white; /* White text on purple background */
}

.subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-left: 0.5rem;
    color: white; /* White subtitle on purple background */
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(94, 46, 96, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(94, 46, 96, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-generate {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
    margin-top: 1rem;
}

/* Main Content Layout */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Tab System */
.tab-content {
    width: 100%;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.quotes-layout {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 2rem;
}

.orders-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-layout {
    max-width: 800px;
}

/* Sidebar Navigation */
.sidebar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.service-nav h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-nav ul {
    list-style: none;
}

.service-nav li {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #666;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    transform: translateX(4px);
}

.nav-link i {
    width: 16px;
    text-align: center;
}

/* Form Area */
.form-area {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Customer Section */
.customer-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
}

.customer-section h2 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.form-group input:required:invalid {
    border-color: #e74c3c;
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #666;
    font-style: italic;
}

/* Services Section */
.services-section {
    margin-top: 2rem;
}

.service-category {
    margin-bottom: 3rem;
    display: none;
}

.service-category.active {
    display: block;
}

.service-category h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #9f7ba0;
}

.services-grid {
    display: grid;
    gap: 2rem;
}

.service-subcategory {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #327881;
}

.service-subcategory h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.service-item {
    margin-bottom: 1rem;
    position: relative;
}

.service-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.service-label:hover {
    border-color: #327881;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d0d0d0;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.service-label input[type="checkbox"]:checked + .checkmark {
    background: #5e2e60;
    border-color: #5e2e60;
}

.service-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.service-name {
    flex: 1;
    font-weight: 500;
    color: #333;
}

.service-price {
    font-weight: 600;
    color: #327881;
    font-size: 1rem;
}

.service-label input[type="checkbox"]:checked ~ .service-name {
    color: #5e2e60;
}

.service-label input[type="checkbox"]:checked {
    border-color: #5e2e60;
    background: rgba(94, 46, 96, 0.05);
}

/* Custom Input Fields */
.custom-input {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f0f0f0;
    border-radius: 6px;
    display: none;
}

.service-item:has(input[type="checkbox"]:checked) .custom-input {
    display: block;
}

.custom-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    color: #666;
}

.custom-input input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Calculator Sidebar */
.calculator {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.calculator-content h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selected-services {
    margin-bottom: 1.5rem;
}

.selected-services h4 {
    color: #666;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.services-list {
    max-height: 200px;
    overflow-y: auto;
}

.no-services {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

.selected-service {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.85rem;
}

.selected-service:last-child {
    border-bottom: none;
}

.service-name-calc {
    flex: 1;
    color: #333;
}

.service-price-calc {
    color: #327881;
    font-weight: 600;
}

/* Discount Section */
.discount-section {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.discount-section h4 {
    margin: 0 0 0.75rem 0;
    color: #5e2e60;
    font-size: 0.95rem;
    font-weight: 600;
}

.discount-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.discount-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
    transition: color 0.2s ease;
}

.discount-option:hover {
    color: #5e2e60;
}

.discount-option input[type="radio"] {
    margin-right: 0.5rem;
    accent-color: #5e2e60;
}

.discount-option span {
    user-select: none;
}

.price-summary {
    border-top: 2px solid #f0f0f0;
    padding-top: 1rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.price-row.discount-row {
    color: #dc3545;
    font-weight: 500;
}

.price-row.total {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
    border-top: 1px solid #e0e0e0;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 200px 1fr 280px;
        gap: 1.5rem;
    }
}

@media (max-width: 992px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .sidebar,
    .calculator {
        position: static;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .form-area,
    .sidebar,
    .calculator {
        padding: 1rem;
    }
    
    .logo-section {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .subtitle {
        margin-left: 0;
    }

    .discount-options {
        flex-direction: column;
        gap: 0.5rem;
    }

    .discount-option {
        justify-content: flex-start;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Dashboard Styles */
.dashboard {
    margin-bottom: 2rem;
}

.dashboard h2 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #327881;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-icon {
    font-size: 2rem;
    color: #5e2e60;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(94, 46, 96, 0.1);
    border-radius: 50%;
}

.dashboard-card .card-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #5e2e60;
    margin: 0;
}

.dashboard-card .card-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Orders Management */
.orders-management {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.orders-header h3 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.orders-actions {
    display: flex;
    gap: 1rem;
}

/* Filters */
.filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

/* Tasks */
.tasks-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.tasks-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tasks-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.task-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.task-item:hover {
    border-color: #327881;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-item.completed {
    opacity: 0.7;
    background: #f8f9fa;
}

.task-checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.task-content {
    flex: 1;
}

.task-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.task-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.task-detail {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.task-actions .btn {
    padding: 0.5rem;
    font-size: 0.8rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 900px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    color: #5e2e60;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

/* Calendar Styles */
.calendar-container {
    width: 100%;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar-header h4 {
    margin: 0;
    color: #5e2e60;
    font-size: 1.2rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 1rem;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #999;
}

.calendar-day.today {
    background: rgba(94, 46, 96, 0.1);
    border: 2px solid #5e2e60;
}

.calendar-day.has-tasks {
    background: rgba(50, 120, 129, 0.1);
}

.calendar-day-number {
    font-weight: 600;
    font-size: 0.9rem;
}

.calendar-task {
    background: #327881;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.7rem;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/* Settings Styles */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 500;
    color: #333;
}

.setting-item input,
.setting-item select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    min-width: 200px;
}

.setting-item input[type="checkbox"] {
    min-width: auto;
    width: 20px;
    height: 20px;
}

/* Enhanced Task Styles */
.task-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.task-item:hover {
    border-color: #327881;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.task-item.task-pending {
    border-left: 4px solid #ffc107;
}

.task-item.task-in_progress {
    border-left: 4px solid #007bff;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.task-item.task-completed {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.task-item.task-cancelled {
    border-left: 4px solid #dc3545;
    opacity: 0.7;
}

.task-status-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.status-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #327881 0%, #5e2e60 100%);
    transition: width 0.3s ease;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-title {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.task-status {
    background: #f8f9fa;
    color: #5e2e60;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.task-progress-steps {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    color: #666;
    font-size: 0.9rem;
}

.step.completed {
    color: #28a745;
    font-weight: 500;
}

.step i {
    width: 20px;
    text-align: center;
}

/* Task Progress Modal Styles */
.task-progress-content {
    max-width: 600px;
}

.task-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.task-info h4 {
    color: #5e2e60;
    margin-bottom: 0.5rem;
}

.task-info p {
    margin: 0.25rem 0;
    color: #666;
}

.progress-steps {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.step-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.step-item.active {
    border-color: #327881;
    background: rgba(50, 120, 129, 0.05);
}

.step-item.completed {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #666;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-item.active .step-icon {
    background: #327881;
    color: white;
}

.step-item.completed .step-icon {
    background: #28a745;
    color: white;
}

.step-content {
    flex: 1;
}

.step-content h5 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.step-content p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.completed-time {
    color: #28a745;
    font-weight: 500;
    font-size: 0.9rem;
}

.completion-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e0e0e0;
}

.completion-section h5 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.completion-section textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-family: inherit;
    resize: vertical;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
}

/* Completion Modal Styles */
.completion-summary {
    text-align: center;
    max-width: 500px;
}

.completion-header {
    margin-bottom: 2rem;
}

.completion-icon {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 1rem;
}

.completion-details {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: left;
}

.completion-details h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.completion-notes {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.completion-notes p {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    font-style: italic;
}

.photo-summary {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.photo-summary span {
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.completion-actions {
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

/* Photo Viewer Styles */
.photos-viewer h4 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    text-align: center;
}

.photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.photo-item {
    text-align: center;
}

.photo-preview {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.photo-timestamp {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #666;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-info {
    border-left: 4px solid #007bff;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-content i {
    color: #28a745;
}

.notification-info .notification-content i {
    color: #007bff;
}

/* No Tasks State */
.no-tasks {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    padding: 2rem;
}

.no-tasks-content {
    text-align: center;
    color: #666;
}

.no-tasks-content i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-tasks-content h4 {
    color: #999;
    margin-bottom: 0.5rem;
}

.no-tasks-content p {
    margin-bottom: 1.5rem;
    color: #999;
}

/* Ensure tasks sections are always visible */
.tasks-container {
    min-height: 400px;
}

.tasks-section {
    margin-bottom: 2rem;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tasks-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f0f0f0;
}

.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Orders List Styles */
.orders-list-container {
    padding: 1rem 0;
}

.orders-list-header {
    margin-bottom: 1.5rem;
}

.orders-search {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.orders-search input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.orders-list-content {
    max-height: 500px;
    overflow-y: auto;
}

.order-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.order-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.order-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.order-id {
    font-size: 0.85rem;
    color: #666;
    font-family: monospace;
}

.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.order-status.active {
    background-color: #d4edda;
    color: #155724;
}

.order-status.completed {
    background-color: #cce5ff;
    color: #004085;
}

.order-status.cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.order-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.order-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #555;
}

.order-detail i {
    color: #327881;
    width: 16px;
}

.order-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.order-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.no-orders {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-orders i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.no-orders h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

/* CRM and Invoicing Styles */

/* Clients Layout */
.clients-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.clients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.clients-header h2 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clients-actions {
    display: flex;
    gap: 1rem;
}

.clients-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #327881;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.stat-card .stat-icon {
    font-size: 2rem;
    color: #5e2e60;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(94, 46, 96, 0.1);
    border-radius: 50%;
}

.stat-card .stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #5e2e60;
    margin: 0;
}

.stat-card .stat-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.clients-filters {
    display: flex;
    gap: 2rem;
    align-items: center;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.clients-filters .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.clients-filters .filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.clients-filters .filter-group input,
.clients-filters .filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 200px;
}

.clients-filters .filter-group input:focus,
.clients-filters .filter-group select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

/* Client Items */
.clients-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.client-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.client-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #327881;
}

.client-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.client-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.client-info {
    flex: 1;
}

.client-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.25rem 0;
}

.client-contact {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.client-contact span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.client-contact i {
    color: #327881;
    width: 14px;
}

.client-address {
    font-size: 0.9rem;
    color: #666;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.client-address i {
    color: #327881;
    width: 14px;
}

.client-stats {
    display: flex;
    gap: 1rem;
}

.client-stats .stat {
    text-align: center;
}

.client-stats .stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
}

.client-stats .stat-label {
    font-size: 0.8rem;
    color: #666;
}

.client-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* No clients state */
.no-clients {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-clients i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-clients h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

/* Invoices Layout */
.invoices-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.invoices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.invoices-header h2 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.invoices-actions {
    display: flex;
    gap: 1rem;
}

.invoices-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.invoices-filters {
    display: flex;
    gap: 2rem;
    align-items: center;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.invoices-filters .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.invoices-filters .filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.invoices-filters .filter-group input,
.invoices-filters .filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 200px;
}

.invoices-filters .filter-group input:focus,
.invoices-filters .filter-group select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

/* Invoice Items */
.invoices-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Invoice Items Header */
.invoice-items-header {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.invoice-items-header .btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
}

/* Available Services Modal */
.service-category-section {
    margin-bottom: 2rem;
}

.category-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #5e2e60;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.service-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.service-header h4 {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
    flex: 1;
}

.service-price {
    font-weight: bold;
    color: #5e2e60;
    font-size: 0.9rem;
}

.service-period {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
    margin: 0.25rem 0;
}

.service-actions {
    text-align: right;
}

/* No items message */
.no-items {
    text-align: center;
    padding: 2rem;
    color: #666;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.invoice-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.invoice-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.invoice-item.invoice-paid {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.02);
}

.invoice-item.invoice-pending {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.02);
}

.invoice-item.invoice-overdue {
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.02);
}

.invoice-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.invoice-number {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #5e2e60;
}

.invoice-number i {
    color: #327881;
}

.invoice-number .number {
    font-family: monospace;
    font-size: 1.1rem;
}

.invoice-client {
    flex: 1;
}

.invoice-client h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
    color: #333;
}

.invoice-date {
    font-size: 0.9rem;
    color: #666;
}

.invoice-amount {
    text-align: right;
}

.invoice-amount .amount {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: #5e2e60;
}

.invoice-amount .status {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    margin-top: 0.25rem;
    display: inline-block;
}

.invoice-amount .status.paid {
    background-color: #d4edda;
    color: #155724;
}

.invoice-amount .status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.invoice-amount .status.overdue {
    background-color: #f8d7da;
    color: #721c24;
}

.invoice-details {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.invoice-details .detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.invoice-details .detail-item i {
    color: #327881;
    width: 16px;
}

.invoice-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* No invoices state */
.no-invoices {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-invoices i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-invoices h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

/* Services Layout */
.services-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.services-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.services-header h2 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.services-actions {
    display: flex;
    gap: 1rem;
}

.services-categories {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.service-category-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.service-category-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #9f7ba0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.service-edit-item {
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    transition: border-color 0.3s ease;
}

.service-edit-item:hover {
    border-color: #327881;
}

.service-edit-item .service-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
}

.service-edit-item .service-price-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-edit-item input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.service-edit-item input:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.service-actions {
    display: flex;
    gap: 0.25rem;
}

.service-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.service-description {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.5rem;
    font-style: italic;
}

/* Large Modal for Invoice Form */
.large-modal .modal-content {
    max-width: 900px;
    width: 95%;
}

.invoice-form-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.invoice-basic-info,
.invoice-items-section,
.invoice-summary,
.invoice-notes {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.invoice-basic-info h4,
.invoice-items-section h4,
.invoice-summary h4,
.invoice-notes h4 {
    color: #5e2e60;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
}

.invoice-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.invoice-items-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.invoice-item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 1rem;
    align-items: center;
    background: white;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.invoice-item-row input,
.invoice-item-row select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.invoice-item-row input:focus,
.invoice-item-row select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.invoice-item-row .remove-item {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.invoice-item-row .remove-item:hover {
    background: #c82333;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e0e0e0;
}

.summary-row.total {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
    border-top: 2px solid #5e2e60;
    border-bottom: none;
    padding-top: 1rem;
    margin-top: 0.5rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e0e0e0;
}

/* Responsive adjustments for CRM */
@media (max-width: 768px) {
    .clients-header,
    .invoices-header,
    .services-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .clients-actions,
    .invoices-actions,
    .services-actions {
        justify-content: center;
    }

    .clients-filters,
    .invoices-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .clients-filters .filter-group input,
    .clients-filters .filter-group select,
    .invoices-filters .filter-group input,
    .invoices-filters .filter-group select {
        min-width: auto;
    }

    .client-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .client-contact {
        flex-direction: column;
        gap: 0.25rem;
    }

    .client-stats {
        justify-content: center;
    }

    .invoice-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .invoice-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .invoice-actions,
    .client-actions {
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .invoice-item-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .large-modal .modal-content {
        width: 98%;
        margin: 1rem;
    }
}

/* Invoiced badge for tasks */
.invoiced-badge {
    background: #28a745;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.invoiced-badge i {
    font-size: 0.7rem;
}

/* Bulk Invoice Modal Styles */
.bulk-invoice-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.bulk-invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.bulk-invoice-header h4 {
    color: #5e2e60;
    margin: 0;
}

.bulk-actions {
    display: flex;
    gap: 0.5rem;
}

.bulk-orders-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.bulk-order-item {
    border-bottom: 1px solid #f0f0f0;
}

.bulk-order-item:last-child {
    border-bottom: none;
}

.bulk-order-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.bulk-order-label:hover {
    background-color: #f8f9fa;
}

.bulk-order-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #5e2e60;
}

.bulk-order-info {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 1rem;
    align-items: center;
}

.bulk-order-info .order-header {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.bulk-order-info .order-id {
    font-weight: 600;
    color: #5e2e60;
    font-family: monospace;
}

.bulk-order-info .order-date {
    font-size: 0.85rem;
    color: #666;
}

.bulk-order-info .order-customer {
    font-weight: 500;
    color: #333;
}

.bulk-order-info .order-service {
    color: #666;
    font-size: 0.9rem;
}

.bulk-order-info .order-total {
    font-weight: 600;
    color: #327881;
    text-align: right;
}

.bulk-summary {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.summary-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1rem;
}

.summary-info strong {
    color: #5e2e60;
}

/* Invoice Detail Modal Styles */
.invoice-detail-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.invoice-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    border-radius: 8px;
}

.invoice-number-large {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.invoice-status-large {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.invoice-status-large.paid {
    background-color: #28a745;
    color: white;
}

.invoice-status-large.pending {
    background-color: #ffc107;
    color: #212529;
}

.invoice-status-large.overdue {
    background-color: #dc3545;
    color: white;
}

.invoice-detail-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.info-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item label {
    font-weight: 500;
    color: #666;
}

.info-item span {
    color: #333;
    font-weight: 500;
}

.client-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}

.client-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.client-contact,
.client-address {
    color: #666;
    margin-bottom: 0.25rem;
}

.invoice-items-detail h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.invoice-items-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.invoice-items-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #5e2e60;
    border-bottom: 1px solid #e0e0e0;
}

.invoice-items-table td {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.invoice-items-table tr:last-child td {
    border-bottom: none;
}

.invoice-totals-detail {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.totals-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e0e0e0;
}

.totals-row.total {
    font-size: 1.2rem;
    font-weight: 600;
    color: #5e2e60;
    border-top: 2px solid #5e2e60;
    border-bottom: none;
    padding-top: 1rem;
    margin-top: 0.5rem;
}

.invoice-notes-detail {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.invoice-notes-detail h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.invoice-notes-detail p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    z-index: 10000;
    opacity: 0;
    transform: translateY(-100%);
    transition: all 0.3s ease;
    max-width: 400px;
    border-left: 4px solid #327881;
}

.notification.notification-success {
    border-left-color: #28a745;
}

.notification.notification-error {
    border-left-color: #dc3545;
}

.notification.notification-warning {
    border-left-color: #ffc107;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-content i {
    font-size: 1.2rem;
    color: #327881;
}

.notification-success .notification-content i {
    color: #28a745;
}

.notification-error .notification-content i {
    color: #dc3545;
}

.notification-warning .notification-content i {
    color: #ffc107;
}

.notification-content span {
    color: #333;
    font-weight: 500;
}

/* Responsive adjustments for notifications */
@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Advanced Statistics Styles */
.advanced-stats-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stats-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.stats-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-section h5 {
    color: #333;
    margin: 1.5rem 0 1rem 0;
    font-size: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

.stat-value {
    color: #5e2e60;
    font-weight: 600;
    font-size: 1.1rem;
}

.top-clients-list,
.popular-services-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.top-client-item,
.popular-service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.client-name,
.service-name {
    color: #333;
    font-weight: 500;
}

.client-revenue,
.service-revenue {
    color: #327881;
    font-weight: 600;
}

.service-rank {
    color: #5e2e60;
    font-weight: 600;
    margin-right: 0.5rem;
    min-width: 20px;
}

/* Settings Styles */
.settings-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.settings-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.settings-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #9f7ba0;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    color: #333;
    font-weight: 500;
    flex: 1;
}

.setting-item input,
.setting-item select {
    max-width: 200px;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.setting-item input:focus,
.setting-item select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.settings-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.settings-actions .btn {
    flex: 1;
    min-width: 150px;
}

/* Responsive adjustments for advanced stats */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .top-client-item,
    .popular-service-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .settings-actions {
        flex-direction: column;
    }

    .settings-actions .btn {
        min-width: auto;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .setting-item input,
    .setting-item select {
        max-width: none;
        width: 100%;
    }
}

/* ===== ENHANCED CRM SYSTEM STYLES ===== */

/* CRM Navigation */
.crm-navigation {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.crm-nav-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.crm-nav-tab {
    padding: 0.75rem 1.5rem;
    border: none;
    background: #f8f9fa;
    color: #666;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.crm-nav-tab:hover {
    background: rgba(94, 46, 96, 0.1);
    color: #5e2e60;
}

.crm-nav-tab.active {
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(94, 46, 96, 0.3);
}

/* Global Search */
.global-search {
    flex: 1;
    max-width: 400px;
}

.search-container {
    position: relative;
}

.search-container i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

#globalSearch {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

#globalSearch:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-suggestion {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.2s ease;
}

.search-suggestion:hover {
    background: #f8f9fa;
}

.search-suggestion:last-child {
    border-bottom: none;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
}

/* CRM Views */
.crm-view {
    display: none;
}

.crm-view.active {
    display: block;
}

/* Dashboard Layout */
.dashboard-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.dashboard-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dashboard-section h2,
.dashboard-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

/* Enhanced Metrics Cards */
.metric-card {
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #5e2e60 0%, #327881 100%);
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.trend-value {
    font-weight: 600;
    color: #28a745;
}

.trend-value.negative {
    color: #dc3545;
}

.trend-period {
    color: #666;
}

.priority-breakdown {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.priority-high,
.priority-medium,
.priority-low {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.priority-high {
    background: #dc3545;
    color: white;
}

.priority-medium {
    background: #ffc107;
    color: #333;
}

.priority-low {
    background: #28a745;
    color: white;
}

.completion-rate {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.conversion-details {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #666;
}

/* Mini Kanban Board */
.mini-kanban {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

.kanban-column {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    min-height: 200px;
}

.kanban-column h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mini-kanban-tasks {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mini-task-card {
    background: white;
    padding: 0.75rem;
    border-radius: 6px;
    border-left: 3px solid #327881;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mini-task-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Upcoming Deadlines */
.upcoming-deadlines {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.deadline-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

.deadline-item.overdue {
    border-left-color: #dc3545;
    background: rgba(220, 53, 69, 0.05);
}

.deadline-item.today {
    border-left-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.deadline-date {
    font-weight: 600;
    color: #5e2e60;
    min-width: 80px;
}

.deadline-content {
    flex: 1;
}

.deadline-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.deadline-description {
    font-size: 0.8rem;
    color: #666;
}

/* Activity Feed */
.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #5e2e60;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-description {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #999;
}

/* Contacts Layout */
.contacts-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contacts-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.contacts-header h2 {
    color: #5e2e60;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contacts-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Contacts Filters */
.contacts-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.filter-actions {
    display: flex;
    align-items: end;
}

/* Contacts Table */
.contacts-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-controls {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bulk-actions label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.table-info {
    font-size: 0.9rem;
    color: #666;
}

.contacts-table {
    width: 100%;
    border-collapse: collapse;
}

.contacts-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #5e2e60;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background 0.2s ease;
}

.contacts-table th:hover {
    background: #e9ecef;
}

.contacts-table th.sortable {
    position: relative;
}

.sort-icon {
    margin-left: 0.5rem;
    opacity: 0.5;
}

.contacts-table th.sorted .sort-icon {
    opacity: 1;
}

.contacts-table td {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.contacts-table tr:hover {
    background: #f8f9fa;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #5e2e60;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 1rem;
}

.contact-info {
    display: flex;
    align-items: center;
}

.contact-name {
    font-weight: 500;
    color: #333;
}

.contact-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.contact-status.lead {
    background: #ffc107;
    color: #333;
}

.contact-status.prospect {
    background: #007bff;
    color: white;
}

.contact-status.customer {
    background: #28a745;
    color: white;
}

.contact-status.inactive {
    background: #6c757d;
    color: white;
}

.table-pagination {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Tasks Layout */
.tasks-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.tasks-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.tasks-header h2 {
    color: #5e2e60;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tasks-view-toggle {
    display: flex;
    gap: 0.5rem;
}

.tasks-view-toggle .btn.active {
    background: #5e2e60;
    color: white;
}

.tasks-actions {
    display: flex;
    gap: 1rem;
}

/* Tasks Filters */
.tasks-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

/* Tasks View Content */
.tasks-view-content {
    display: none;
}

.tasks-view-content.active {
    display: block;
}

/* Kanban Board */
.kanban-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.kanban-column {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.kanban-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
}

.kanban-header h3 {
    color: #5e2e60;
    margin: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.task-count {
    background: #5e2e60;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.kanban-tasks {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 200px;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.2s ease;
}

.kanban-tasks.drag-over {
    background: rgba(94, 46, 96, 0.1);
}

.kanban-task-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: grab;
    transition: all 0.3s ease;
    border-left: 4px solid #327881;
}

.kanban-task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.kanban-task-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.kanban-task-card.high-priority {
    border-left-color: #dc3545;
}

.kanban-task-card.medium-priority {
    border-left-color: #ffc107;
}

.kanban-task-card.low-priority {
    border-left-color: #28a745;
}

.task-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.task-card-title {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.3;
}

.task-card-priority {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.task-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.8rem;
    color: #666;
}

.task-card-assignee {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.assignee-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #5e2e60;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

.task-card-deadline {
    font-size: 0.8rem;
}

.task-card-deadline.overdue {
    color: #dc3545;
    font-weight: 600;
}

.task-card-deadline.today {
    color: #007bff;
    font-weight: 600;
}

.task-card-labels {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.task-label {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    background: #e9ecef;
    color: #495057;
}

.task-label.urgent {
    background: #dc3545;
    color: white;
}

.task-label.cleaning {
    background: #007bff;
    color: white;
}

.task-label.maintenance {
    background: #28a745;
    color: white;
}

.task-label.follow-up {
    background: #ffc107;
    color: #333;
}

.add-task-btn {
    margin-top: 1rem;
    padding: 0.75rem;
    border: 2px dashed #ccc;
    background: transparent;
    color: #666;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.add-task-btn:hover {
    border-color: #5e2e60;
    color: #5e2e60;
    background: rgba(94, 46, 96, 0.05);
}

/* Pipeline Styles */
.pipeline-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.pipeline-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pipeline-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #5e2e60;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

.pipeline-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.pipeline-board {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1.5rem;
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.pipeline-stage {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 500px;
    min-width: 250px;
    display: flex;
    flex-direction: column;
}

.stage-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
}

.stage-header h3 {
    color: #5e2e60;
    margin: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stage-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.deals-count {
    background: #5e2e60;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.stage-value {
    color: #327881;
    font-weight: 600;
}

.stage-deals {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 200px;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.2s ease;
}

.pipeline-deal-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: grab;
    transition: all 0.3s ease;
    border-left: 4px solid #327881;
}

.pipeline-deal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.deal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.deal-title {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.3;
}

.deal-value {
    font-weight: 600;
    color: #327881;
    font-size: 0.9rem;
}

.deal-company {
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 0.75rem;
}

.deal-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.deal-probability {
    color: #5e2e60;
    font-weight: 500;
}

.add-deal-btn {
    margin-top: 1rem;
    padding: 0.75rem;
    border: 2px dashed #ccc;
    background: transparent;
    color: #666;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.add-deal-btn:hover {
    border-color: #5e2e60;
    color: #5e2e60;
    background: rgba(94, 46, 96, 0.05);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .crm-navigation {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .crm-nav-tabs {
        justify-content: center;
    }

    .global-search {
        max-width: none;
    }

    .kanban-board {
        grid-template-columns: repeat(2, 1fr);
    }

    .pipeline-board {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .crm-navigation {
        padding: 1rem;
    }

    .crm-nav-tabs {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .crm-nav-tab {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .mini-kanban {
        grid-template-columns: repeat(2, 1fr);
    }

    .kanban-board {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .pipeline-board {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contacts-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .contacts-table {
        font-size: 0.8rem;
    }

    .contacts-table th,
    .contacts-table td {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .mini-kanban {
        grid-template-columns: 1fr;
    }

    .contacts-header {
        flex-direction: column;
        align-items: stretch;
    }

    .contacts-actions {
        justify-content: center;
    }

    .table-controls {
        flex-direction: column;
        gap: 1rem;
    }
}

/* CRM Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 400px;
    border-left: 4px solid #327881;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.notification-success {
    border-left-color: #28a745;
}

.notification.notification-error {
    border-left-color: #dc3545;
}

.notification.notification-warning {
    border-left-color: #ffc107;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-content i {
    font-size: 1.2rem;
    color: #327881;
}

.notification-success .notification-content i {
    color: #28a745;
}

.notification-error .notification-content i {
    color: #dc3545;
}

.notification-warning .notification-content i {
    color: #ffc107;
}

.notification-content span {
    font-size: 0.9rem;
    color: #333;
    line-height: 1.4;
}

/* Enhanced Calendar Styles */
.calendar-layout {
    padding: 1.5rem;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.calendar-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.calendar-controls h3 {
    margin: 0;
    min-width: 150px;
    text-align: center;
    color: #5e2e60;
}

.calendar-view-options {
    display: flex;
    gap: 0.5rem;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e0e0e0;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.weekday {
    background: #5e2e60;
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e0e0e0;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 0.75rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-day:hover {
    background: #f8f9fa;
}

.calendar-day.empty {
    background: #f5f5f5;
    cursor: default;
}

.calendar-day.today {
    background: rgba(94, 46, 96, 0.1);
    border: 2px solid #5e2e60;
}

.calendar-day.has-items {
    background: rgba(50, 120, 129, 0.05);
}

.calendar-day-number {
    font-weight: 600;
    font-size: 1rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.calendar-items {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.calendar-item {
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.calendar-item.task {
    background: #327881;
    color: white;
}

.calendar-item.task.high-priority {
    background: #e74c3c;
}

.calendar-item.task.urgent-priority {
    background: #c0392b;
}

.calendar-item.event {
    background: #5e2e60;
    color: white;
}

.calendar-more {
    font-size: 0.7rem;
    color: #666;
    font-style: italic;
}

/* Notes Styles */
.notes-layout {
    padding: 1.5rem;
}

.notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.notes-filters {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.notes-filters .form-group {
    margin: 0;
    min-width: 200px;
}

.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.note-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.note-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #5e2e60;
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.note-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    line-height: 1.3;
    flex: 1;
    margin-right: 1rem;
}

.note-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.note-card:hover .note-actions {
    opacity: 1;
}

.note-content {
    color: #666;
    line-height: 1.5;
    margin-bottom: 1rem;
    white-space: pre-wrap;
}

.note-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #888;
    margin-bottom: 1rem;
}

.note-category,
.note-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.note-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.note-tag {
    background: #f0f0f0;
    color: #666;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.notes-empty {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #ddd;
}

.notes-empty i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
    display: block;
}

.notes-empty h3 {
    margin: 1rem 0;
    color: #333;
}

/* Day Details Modal */
.day-tasks,
.day-events {
    margin-bottom: 1.5rem;
}

.day-item {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
}

.day-item.task {
    border-left: 4px solid #327881;
}

.day-item.event {
    border-left: 4px solid #5e2e60;
}

.item-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.item-meta {
    font-size: 0.9rem;
    color: #666;
}

/* Drag and Drop Enhancements */
.kanban-tasks.drag-over {
    background: rgba(94, 46, 96, 0.1);
    border: 2px dashed #5e2e60;
    border-radius: 8px;
}

.kanban-task-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* No data states */
.no-deadlines,
.no-activity {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.no-deadlines i,
.no-activity i {
    font-size: 2rem;
    color: #ddd;
    margin-bottom: 1rem;
    display: block;
}

/* Enhanced form styles for CRM modals */
.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }
}
