// Enhanced CRM System
// Comprehensive CRM functionality with task management, contacts, pipeline, and calendar

// Global CRM Data
let crmData = {
    contacts: [],
    tasks: [],
    deals: [],
    events: [],
    notes: [],
    currentView: 'dashboard'
};

// CRM System Initialization
document.addEventListener('DOMContentLoaded', function() {
    // Wait for other systems to initialize first
    setTimeout(() => {
        initializeCRMSystem();
        loadCRMData();
        updateCRMDashboard();
    }, 1000);
});

function initializeCRMSystem() {
    console.log('Initializing CRM System...');

    // Initialize CRM navigation
    const crmNavTabs = document.querySelectorAll('.crm-nav-tab');
    crmNavTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const view = this.dataset.crmView;
            switchCRMView(view);
        });
    });

    // Initialize global search
    const globalSearch = document.getElementById('globalSearch');
    if (globalSearch) {
        globalSearch.addEventListener('input', handleGlobalSearch);
        globalSearch.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                hideSearchSuggestions();
            }
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            globalSearch?.focus();
        }
    });

    // Initialize drag and drop for Kanban
    initializeDragAndDrop();

    console.log('CRM System initialized successfully');
}

function switchCRMView(viewName) {
    // Update navigation
    document.querySelectorAll('.crm-nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-crm-view="${viewName}"]`).classList.add('active');

    // Update views
    document.querySelectorAll('.crm-view').forEach(view => {
        view.classList.remove('active');
    });
    document.getElementById(`crm-${viewName}`).classList.add('active');

    crmData.currentView = viewName;

    // Load view-specific data
    switch(viewName) {
        case 'dashboard':
            updateCRMDashboard();
            break;
        case 'contacts':
            renderContactsTable();
            break;
        case 'tasks':
            renderKanbanBoard();
            break;
        case 'pipeline':
            renderPipelineBoard();
            break;
        case 'calendar':
            renderCalendar();
            break;
        case 'notes':
            renderNotesGrid();
            break;
    }
}

// Global Search Functionality
function handleGlobalSearch(e) {
    const query = e.target.value.toLowerCase();
    if (query.length < 2) {
        hideSearchSuggestions();
        return;
    }

    const suggestions = [];

    // Search contacts
    crmData.contacts.forEach(contact => {
        if (contact.name.toLowerCase().includes(query) || 
            contact.email.toLowerCase().includes(query) ||
            contact.company.toLowerCase().includes(query)) {
            suggestions.push({
                type: 'contact',
                title: contact.name,
                subtitle: contact.company,
                icon: 'fas fa-user',
                action: () => showContactDetail(contact.id)
            });
        }
    });

    // Search tasks
    crmData.tasks.forEach(task => {
        if (task.title.toLowerCase().includes(query) ||
            task.description.toLowerCase().includes(query)) {
            suggestions.push({
                type: 'task',
                title: task.title,
                subtitle: `Status: ${task.status}`,
                icon: 'fas fa-tasks',
                action: () => showTaskDetail(task.id)
            });
        }
    });

    // Search deals
    crmData.deals.forEach(deal => {
        if (deal.title.toLowerCase().includes(query) ||
            deal.company.toLowerCase().includes(query)) {
            suggestions.push({
                type: 'deal',
                title: deal.title,
                subtitle: `${deal.value}€ - ${deal.stage}`,
                icon: 'fas fa-chart-line',
                action: () => showDealDetail(deal.id)
            });
        }
    });

    showSearchSuggestions(suggestions.slice(0, 8));
}

function showSearchSuggestions(suggestions) {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (!suggestionsContainer) return;

    if (suggestions.length === 0) {
        hideSearchSuggestions();
        return;
    }

    const html = suggestions.map(suggestion => `
        <div class="search-suggestion" onclick="executeSearchAction('${suggestion.type}', '${suggestion.action}')">
            <i class="${suggestion.icon}"></i>
            <div class="suggestion-content">
                <div class="suggestion-title">${suggestion.title}</div>
                <div class="suggestion-subtitle">${suggestion.subtitle}</div>
            </div>
        </div>
    `).join('');

    suggestionsContainer.innerHTML = html;
    suggestionsContainer.style.display = 'block';
}

function hideSearchSuggestions() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
}

// Dashboard Functions
function updateCRMDashboard() {
    updateDashboardMetrics();
    updateMiniKanban();
    updateUpcomingDeadlines();
    updateActivityFeed();
}

function updateDashboardMetrics() {
    // Total contacts with trend
    const totalContacts = crmData.contacts.length;
    const contactsTrend = calculateContactsTrend();

    const totalContactsEl = document.getElementById('totalContacts');
    const contactsTrendEl = document.getElementById('contactsTrend');

    if (totalContactsEl) totalContactsEl.textContent = totalContacts;
    if (contactsTrendEl) contactsTrendEl.textContent = `+${contactsTrend}`;

    // Active tasks by priority
    const activeTasks = crmData.tasks.filter(task => task.status !== 'done');
    const highPriority = activeTasks.filter(task => task.priority === 'high').length;
    const mediumPriority = activeTasks.filter(task => task.priority === 'medium').length;
    const lowPriority = activeTasks.filter(task => task.priority === 'low').length;

    const activeTasksEl = document.getElementById('activeTasks');
    const highPriorityEl = document.getElementById('highPriorityTasks');
    const mediumPriorityEl = document.getElementById('mediumPriorityTasks');
    const lowPriorityEl = document.getElementById('lowPriorityTasks');

    if (activeTasksEl) activeTasksEl.textContent = activeTasks.length;
    if (highPriorityEl) highPriorityEl.textContent = highPriority;
    if (mediumPriorityEl) mediumPriorityEl.textContent = mediumPriority;
    if (lowPriorityEl) lowPriorityEl.textContent = lowPriority;

    // Completed this week
    const completedThisWeek = calculateCompletedThisWeek();
    const completionRate = calculateCompletionRate();

    const completedThisWeekEl = document.getElementById('completedThisWeek');
    const completionRateEl = document.getElementById('completionRate');

    if (completedThisWeekEl) completedThisWeekEl.textContent = completedThisWeek;
    if (completionRateEl) completionRateEl.textContent = `${completionRate}%`;

    // Conversion rate
    const conversionRate = calculateConversionRate();
    const conversionRateEl = document.getElementById('conversionRate');

    if (conversionRateEl) conversionRateEl.textContent = `${conversionRate}%`;

    console.log('Dashboard metrics updated:', {
        totalContacts,
        activeTasks: activeTasks.length,
        completedThisWeek,
        completionRate,
        conversionRate
    });
}

function calculateContactsTrend() {
    const thisMonth = new Date().getMonth();
    const thisYear = new Date().getFullYear();
    return crmData.contacts.filter(contact => {
        const contactDate = new Date(contact.createdAt);
        return contactDate.getMonth() === thisMonth && contactDate.getFullYear() === thisYear;
    }).length;
}

function calculateCompletedThisWeek() {
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);

    return crmData.tasks.filter(task => {
        if (task.status !== 'done' || !task.completedAt) return false;
        const completedDate = new Date(task.completedAt);
        return completedDate >= weekStart;
    }).length;
}

function calculateCompletionRate() {
    const totalTasks = crmData.tasks.length;
    if (totalTasks === 0) return 0;
    const completedTasks = crmData.tasks.filter(task => task.status === 'done').length;
    return Math.round((completedTasks / totalTasks) * 100);
}

function calculateConversionRate() {
    const leads = crmData.contacts.filter(contact => contact.status === 'lead').length;
    const customers = crmData.contacts.filter(contact => contact.status === 'customer').length;
    if (leads === 0) return 0;
    return Math.round((customers / (leads + customers)) * 100);
}

// Mini Kanban for Dashboard
function updateMiniKanban() {
    const tasksByStatus = {
        new: crmData.tasks.filter(task => task.status === 'new').slice(0, 3),
        in_progress: crmData.tasks.filter(task => task.status === 'in_progress').slice(0, 3),
        review: crmData.tasks.filter(task => task.status === 'review').slice(0, 3),
        done: crmData.tasks.filter(task => task.status === 'done').slice(0, 3)
    };

    Object.keys(tasksByStatus).forEach(status => {
        const container = document.getElementById(`miniKanban${status.charAt(0).toUpperCase() + status.slice(1).replace('_', '')}`);
        if (container) {
            container.innerHTML = tasksByStatus[status].map(task => `
                <div class="mini-task-card" onclick="showTaskDetail('${task.id}')">
                    <div class="mini-task-title">${task.title}</div>
                    <div class="mini-task-meta">${task.assignee || 'Nepridelené'}</div>
                </div>
            `).join('');
        }
    });
}

// Upcoming Deadlines
function updateUpcomingDeadlines() {
    const container = document.getElementById('upcomingDeadlines');
    if (!container) return;

    const today = new Date();
    const nextWeek = new Date();
    nextWeek.setDate(today.getDate() + 7);

    const upcomingTasks = crmData.tasks.filter(task => {
        if (!task.deadline) return false;
        const deadline = new Date(task.deadline);
        return deadline >= today && deadline <= nextWeek;
    }).sort((a, b) => new Date(a.deadline) - new Date(b.deadline));

    if (upcomingTasks.length === 0) {
        container.innerHTML = `
            <div class="no-deadlines">
                <i class="fas fa-calendar-check"></i>
                <p>Žiadne nadchádzajúce termíny v najbližších 7 dňoch</p>
            </div>
        `;
        return;
    }

    container.innerHTML = upcomingTasks.map(task => {
        const deadline = new Date(task.deadline);
        const isToday = deadline.toDateString() === today.toDateString();
        const isOverdue = deadline < today;

        return `
            <div class="deadline-item ${isOverdue ? 'overdue' : isToday ? 'today' : ''}">
                <div class="deadline-date">${formatDate(task.deadline)}</div>
                <div class="deadline-content">
                    <div class="deadline-title">${task.title}</div>
                    <div class="deadline-description">${task.assignee || 'Nepridelené'} - ${task.priority} priorita</div>
                </div>
            </div>
        `;
    }).join('');
}

// Activity Feed
function updateActivityFeed() {
    const container = document.getElementById('activityFeed');
    if (!container) return;

    // Create sample activity items based on recent data
    const activities = [];

    // Recent contacts
    const recentContacts = crmData.contacts
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 3);

    recentContacts.forEach(contact => {
        activities.push({
            type: 'contact',
            icon: 'fas fa-user-plus',
            title: 'Nový kontakt pridaný',
            description: `${contact.name} z ${contact.company}`,
            time: contact.createdAt
        });
    });

    // Recent tasks
    const recentTasks = crmData.tasks
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 3);

    recentTasks.forEach(task => {
        activities.push({
            type: 'task',
            icon: 'fas fa-tasks',
            title: 'Nová úloha vytvorená',
            description: task.title,
            time: task.createdAt
        });
    });

    // Sort by time
    activities.sort((a, b) => new Date(b.time) - new Date(a.time));

    if (activities.length === 0) {
        container.innerHTML = `
            <div class="no-activity">
                <i class="fas fa-history"></i>
                <p>Žiadna nedávna aktivita</p>
            </div>
        `;
        return;
    }

    container.innerHTML = activities.slice(0, 5).map(activity => `
        <div class="activity-item">
            <div class="activity-icon">
                <i class="${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${activity.title}</div>
                <div class="activity-description">${activity.description}</div>
                <div class="activity-time">${formatRelativeTime(activity.time)}</div>
            </div>
        </div>
    `).join('');
}

function formatRelativeTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Práve teraz';
    if (diffMins < 60) return `Pred ${diffMins} min`;
    if (diffHours < 24) return `Pred ${diffHours} h`;
    if (diffDays < 7) return `Pred ${diffDays} dňami`;
    return formatDate(dateString);
}

// Data Management
function loadCRMData() {
    console.log('Loading CRM data...');

    // Load existing CRM data
    const savedData = localStorage.getItem('crm_system_data');
    if (savedData) {
        crmData = { ...crmData, ...JSON.parse(savedData) };
    }

    // Integrate with existing orders system
    integrateWithExistingData();

    // If no data exists, initialize with sample data
    if (crmData.contacts.length === 0 && crmData.tasks.length === 0) {
        initializeSampleData();
    }

    console.log('CRM data loaded:', crmData);
}

function integrateWithExistingData() {
    // Check if orders system is available
    if (typeof orders !== 'undefined' && orders.length > 0) {
        console.log('Integrating with existing orders:', orders.length);

        // Convert orders to contacts and tasks
        orders.forEach(order => {
            // Create contact from order customer
            const existingContact = crmData.contacts.find(c => c.email === order.customer.email);
            if (!existingContact) {
                const contact = {
                    id: generateId(),
                    name: order.customer.name,
                    email: order.customer.email,
                    phone: order.customer.phone || '',
                    company: order.customer.company || '',
                    status: 'customer',
                    dealValue: order.totalPrice || 0,
                    lastContact: order.startDate,
                    assignedUser: 'me',
                    createdAt: order.createdAt || new Date().toISOString(),
                    notes: `Objednávka: ${order.package.name}`
                };
                crmData.contacts.push(contact);
            }
        });
    }

    // Check if tasks system is available
    if (typeof tasks !== 'undefined' && tasks.length > 0) {
        console.log('Integrating with existing tasks:', tasks.length);

        // Convert existing tasks to CRM tasks
        tasks.forEach(task => {
            const existingTask = crmData.tasks.find(t => t.originalId === task.id);
            if (!existingTask) {
                const crmTask = {
                    id: generateId(),
                    originalId: task.id,
                    title: task.description || `${task.type} - ${task.customer}`,
                    description: `Úloha pre ${task.customer} - ${task.location}`,
                    status: mapTaskStatus(task.status),
                    priority: 'medium',
                    assignee: task.assignedTo || '',
                    deadline: task.date,
                    contactId: findContactByName(task.customer)?.id || '',
                    labels: [task.type],
                    createdAt: task.createdAt || new Date().toISOString(),
                    updatedAt: task.updatedAt || new Date().toISOString()
                };
                crmData.tasks.push(crmTask);
            }
        });
    }
}

function mapTaskStatus(originalStatus) {
    const statusMap = {
        'pending': 'new',
        'in_progress': 'in_progress',
        'completed': 'done',
        'cancelled': 'done'
    };
    return statusMap[originalStatus] || 'new';
}

function findContactByName(customerName) {
    if (!customerName || typeof customerName !== 'string') return null;
    return crmData.contacts.find(contact =>
        contact.name && contact.name.toLowerCase().includes(customerName.toLowerCase())
    );
}

function saveCRMData() {
    localStorage.setItem('crm_system_data', JSON.stringify(crmData));
    console.log('CRM data saved to localStorage');
}

// Synchronization with existing orders system
function syncWithOrdersSystem() {
    // This function will be called when orders are updated
    if (typeof orders !== 'undefined') {
        integrateWithExistingData();
        updateCRMDashboard();

        // Update current view if needed
        if (crmData.currentView === 'contacts') {
            renderContactsTable();
        } else if (crmData.currentView === 'tasks') {
            renderKanbanBoard();
        }
    }
}

// Make sync function available globally
window.syncCRMWithOrders = syncWithOrdersSystem;

function initializeSampleData() {
    console.log('Initializing sample CRM data...');

    // Only add sample data if no existing data
    if (crmData.contacts.length === 0) {
        // Sample contacts
        crmData.contacts = [
            {
                id: generateId(),
                name: 'Ján Novák',
                email: '<EMAIL>',
                phone: '+*********** 567',
                company: 'ABC s.r.o.',
                status: 'customer',
                dealValue: 1500,
                lastContact: '2025-01-02',
                assignedUser: 'me',
                createdAt: '2024-12-15'
            },
            {
                id: generateId(),
                name: 'Mária Svobodová',
                email: '<EMAIL>',
                phone: '+*********** 678',
                company: 'XYZ spol. s r.o.',
                status: 'lead',
                dealValue: 800,
                lastContact: '2025-01-01',
                assignedUser: 'me',
                createdAt: '2025-01-01'
            }
        ];
    }

    if (crmData.tasks.length === 0) {
        // Sample tasks
        crmData.tasks = [
            {
                id: generateId(),
                title: 'Kontaktovať nového klienta',
                description: 'Zavolať a dohodnúť stretnutie',
                status: 'new',
                priority: 'high',
                assignee: 'Ján Novák',
                deadline: '2025-01-05',
                labels: ['urgent', 'follow-up'],
                createdAt: '2025-01-03'
            },
            {
                id: generateId(),
                title: 'Pripraviť cenovú ponuku',
                description: 'Vypracovať detailnú cenovú ponuku pre ABC s.r.o.',
                status: 'in_progress',
                priority: 'medium',
                assignee: 'Mária Svobodová',
                deadline: '2025-01-07',
                labels: ['cleaning'],
                createdAt: '2025-01-02'
            }
        ];
    }

    if (crmData.deals.length === 0) {
        // Sample deals
        crmData.deals = [
            {
                id: generateId(),
                title: 'Celoročná starostlivosť - ABC s.r.o.',
                value: 1500,
                stage: 'qualified',
                probability: 80,
                company: 'ABC s.r.o.',
                expectedCloseDate: '2025-01-15',
                description: 'Celoročný balíček pre údržbu hrobových miest',
                createdAt: '2024-12-20',
                updatedAt: '2025-01-02'
            }
        ];
    }

    saveCRMData();
    console.log('Sample data initialized');
}

function generateId() {
    return 'crm_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// Contacts Management
function renderContactsTable() {
    const tableBody = document.getElementById('contactsTableBody');
    if (!tableBody) return;

    const html = crmData.contacts.map(contact => `
        <tr>
            <td>
                <input type="checkbox" class="contact-checkbox" value="${contact.id}">
            </td>
            <td>
                <div class="contact-info">
                    <div class="contact-avatar">${contact.name.charAt(0).toUpperCase()}</div>
                    <div class="contact-name">${contact.name}</div>
                </div>
            </td>
            <td>${contact.company}</td>
            <td>${contact.email}</td>
            <td>${contact.phone}</td>
            <td>
                <span class="contact-status ${contact.status}">${getStatusLabel(contact.status)}</span>
            </td>
            <td>${formatDate(contact.lastContact)}</td>
            <td>${formatPrice(contact.dealValue)}</td>
            <td>
                <button class="btn btn-secondary btn-sm" onclick="editContact('${contact.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-secondary btn-sm" onclick="deleteContact('${contact.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = html;
    updateContactsCount();
}

function getStatusLabel(status) {
    const labels = {
        lead: 'Lead',
        prospect: 'Prospect',
        customer: 'Zákazník',
        inactive: 'Neaktívny'
    };
    return labels[status] || status;
}

function updateContactsCount() {
    const count = crmData.contacts.length;
    const countElement = document.getElementById('contactsCount');
    if (countElement) {
        countElement.textContent = `${count} kontaktov`;
    }
}

// Kanban Board Management
function renderKanbanBoard() {
    const statuses = ['new', 'in_progress', 'review', 'done'];

    statuses.forEach(status => {
        const container = document.getElementById(`kanban${status.charAt(0).toUpperCase() + status.slice(1).replace('_', '')}`);
        if (!container) return;

        const tasks = crmData.tasks.filter(task => task.status === status);

        container.innerHTML = tasks.map(task => `
            <div class="kanban-task-card ${task.priority}-priority" draggable="true" data-task-id="${task.id}">
                <div class="task-card-header">
                    <div class="task-card-title">${task.title}</div>
                    <div class="task-card-priority ${task.priority}">${getPriorityLabel(task.priority)}</div>
                </div>
                <div class="task-card-description">${task.description}</div>
                <div class="task-card-meta">
                    <div class="task-card-assignee">
                        <div class="assignee-avatar">${task.assignee ? task.assignee.charAt(0).toUpperCase() : '?'}</div>
                        <span>${task.assignee || 'Nepridelené'}</span>
                    </div>
                    <div class="task-card-deadline ${getDeadlineClass(task.deadline)}">
                        ${formatDate(task.deadline)}
                    </div>
                </div>
                ${task.labels ? `
                    <div class="task-card-labels">
                        ${task.labels.map(label => `<span class="task-label ${label}">${label}</span>`).join('')}
                    </div>
                ` : ''}
            </div>
        `).join('');

        // Update task count
        const countElement = document.getElementById(`${status}TasksCount`);
        if (countElement) {
            countElement.textContent = tasks.length;
        }
    });
}

function getPriorityLabel(priority) {
    const labels = {
        high: 'Vysoká',
        medium: 'Stredná',
        low: 'Nízka'
    };
    return labels[priority] || priority;
}

function getDeadlineClass(deadline) {
    if (!deadline) return '';

    const today = new Date();
    const deadlineDate = new Date(deadline);

    if (deadlineDate < today) return 'overdue';
    if (deadlineDate.toDateString() === today.toDateString()) return 'today';
    return '';
}

// Drag and Drop for Kanban
function initializeDragAndDrop() {
    document.addEventListener('dragstart', function(e) {
        if (e.target.classList.contains('kanban-task-card')) {
            e.target.classList.add('dragging');
            e.dataTransfer.setData('text/plain', e.target.dataset.taskId);
        }
    });

    document.addEventListener('dragend', function(e) {
        if (e.target.classList.contains('kanban-task-card')) {
            e.target.classList.remove('dragging');
        }
    });
}

function allowDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
}

function dropTask(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');

    const taskId = e.dataTransfer.getData('text/plain');
    const newStatus = e.currentTarget.closest('.kanban-column').dataset.status;

    updateTaskStatus(taskId, newStatus);
}

function updateTaskStatus(taskId, newStatus) {
    const task = crmData.tasks.find(t => t.id === taskId);
    if (task) {
        task.status = newStatus;
        task.updatedAt = new Date().toISOString();

        if (newStatus === 'done') {
            task.completedAt = new Date().toISOString();
        }

        saveCRMData();
        renderKanbanBoard();
        updateCRMDashboard();

        showNotification(`Úloha "${task.title}" bola presunutá do stavu: ${getStatusLabel(newStatus)}`, 'success');
    }
}

// Pipeline Management
function renderPipelineBoard() {
    const stages = ['lead', 'qualified', 'presentation', 'proposal', 'negotiation', 'closed'];

    stages.forEach(stage => {
        const container = document.getElementById(`${stage}Deals`);
        if (!container) return;

        const deals = crmData.deals.filter(deal => deal.stage === stage);

        container.innerHTML = deals.map(deal => `
            <div class="pipeline-deal-card" draggable="true" data-deal-id="${deal.id}">
                <div class="deal-header">
                    <div class="deal-title">${deal.title}</div>
                    <div class="deal-value">${formatPrice(deal.value)}</div>
                </div>
                <div class="deal-company">${deal.company}</div>
                <div class="deal-meta">
                    <div class="deal-probability">${deal.probability}% šanca</div>
                    <div class="deal-close-date">${formatDate(deal.expectedCloseDate)}</div>
                </div>
                <div class="deal-actions">
                    <button class="btn btn-secondary btn-sm" onclick="editDeal('${deal.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </div>
        `).join('');

        // Update stage stats
        const countElement = document.getElementById(`${stage}DealsCount`);
        const valueElement = document.getElementById(`${stage}StageValue`);

        if (countElement) countElement.textContent = deals.length;
        if (valueElement) {
            const totalValue = deals.reduce((sum, deal) => sum + deal.value, 0);
            valueElement.textContent = formatPrice(totalValue);
        }
    });
}

// Utility Functions
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('sk-SK');
}

function formatPrice(price) {
    if (!price) return '0,00 €';
    return new Intl.NumberFormat('sk-SK', {
        style: 'currency',
        currency: 'EUR'
    }).format(price);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Modal Functions
function showAddContactModal() {
    document.getElementById('addContactModal').style.display = 'block';
}

function showAddTaskModal(defaultStatus = 'new') {
    const modal = document.getElementById('addTaskModal');
    const statusSelect = document.getElementById('taskStatus');

    if (statusSelect) {
        statusSelect.value = defaultStatus;
    }

    // Populate contact dropdown
    populateContactDropdown();

    modal.style.display = 'block';
}

function showAddDealModal(defaultStage = 'lead') {
    const modal = document.getElementById('addDealModal');
    const stageSelect = document.getElementById('dealStage');

    if (stageSelect) {
        stageSelect.value = defaultStage;
    }

    modal.style.display = 'block';
}

function populateContactDropdown() {
    const select = document.getElementById('taskContact');
    if (!select) return;

    const options = crmData.contacts.map(contact =>
        `<option value="${contact.id}">${contact.name} - ${contact.company}</option>`
    ).join('');

    select.innerHTML = '<option value="">Vyberte kontakt...</option>' + options;
}

// Form Handlers
function handleAddContact(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const contact = {
        id: generateId(),
        name: formData.get('name'),
        email: formData.get('email'),
        phone: formData.get('phone') || '',
        company: formData.get('company') || '',
        status: formData.get('status'),
        dealValue: parseFloat(formData.get('dealValue')) || 0,
        notes: formData.get('notes') || '',
        lastContact: new Date().toISOString().split('T')[0],
        assignedUser: 'me',
        createdAt: new Date().toISOString()
    };

    crmData.contacts.push(contact);
    saveCRMData();

    // Update UI
    if (crmData.currentView === 'contacts') {
        renderContactsTable();
    }
    updateCRMDashboard();

    // Close modal and reset form
    closeModal('addContactModal');
    event.target.reset();

    showNotification(`Kontakt "${contact.name}" bol úspešne pridaný`, 'success');
}

function handleAddTask(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const labels = formData.get('labels') ? formData.get('labels').split(',').map(l => l.trim()) : [];

    const task = {
        id: generateId(),
        title: formData.get('title'),
        description: formData.get('description') || '',
        status: formData.get('status'),
        priority: formData.get('priority'),
        assignee: formData.get('assignee') || '',
        deadline: formData.get('deadline') || '',
        contactId: formData.get('contactId') || '',
        labels: labels,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    crmData.tasks.push(task);
    saveCRMData();

    // Update UI
    if (crmData.currentView === 'tasks') {
        renderKanbanBoard();
    }
    updateCRMDashboard();

    // Close modal and reset form
    closeModal('addTaskModal');
    event.target.reset();

    showNotification(`Úloha "${task.title}" bola úspešne vytvorená`, 'success');
}

function handleAddDeal(event) {
    event.preventDefault();

    const formData = new FormData(event.target);

    const deal = {
        id: generateId(),
        title: formData.get('title'),
        value: parseFloat(formData.get('value')),
        stage: formData.get('stage'),
        probability: parseInt(formData.get('probability')),
        company: formData.get('company') || '',
        expectedCloseDate: formData.get('expectedCloseDate') || '',
        description: formData.get('description') || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    crmData.deals.push(deal);
    saveCRMData();

    // Update UI
    if (crmData.currentView === 'pipeline') {
        renderPipelineBoard();
    }
    updateCRMDashboard();

    // Close modal and reset form
    closeModal('addDealModal');
    event.target.reset();

    showNotification(`Príležitosť "${deal.title}" bola úspešne vytvorená`, 'success');
}

// Placeholder functions for future implementation
function showContactDetail(contactId) {
    console.log('Show contact detail:', contactId);
    // TODO: Implement contact detail modal
}

function showTaskDetail(taskId) {
    console.log('Show task detail:', taskId);
    // TODO: Implement task detail modal
}

function showDealDetail(dealId) {
    console.log('Show deal detail:', dealId);
    // TODO: Implement deal detail modal
}

function editContact(contactId) {
    console.log('Edit contact:', contactId);
    // TODO: Implement edit contact functionality
}

function deleteContact(contactId) {
    if (confirm('Naozaj chcete vymazať tento kontakt?')) {
        crmData.contacts = crmData.contacts.filter(c => c.id !== contactId);
        saveCRMData();
        renderContactsTable();
        updateCRMDashboard();
        showNotification('Kontakt bol vymazaný', 'success');
    }
}

function editDeal(dealId) {
    console.log('Edit deal:', dealId);
    // TODO: Implement edit deal functionality
}

function dropDeal(event) {
    // TODO: Implement deal drag and drop
    console.log('Drop deal:', event);
}

// Calendar Functions
function renderCalendar() {
    console.log('Rendering calendar view');
    // TODO: Implement full calendar functionality
    const container = document.getElementById('calendarDays');
    if (container) {
        container.innerHTML = '<div class="calendar-placeholder">Kalendár bude implementovaný v budúcej verzii</div>';
    }
}

// Notes Functions
function renderNotesGrid() {
    console.log('Rendering notes grid');
    // TODO: Implement notes functionality
    const container = document.getElementById('notesGrid');
    if (container) {
        container.innerHTML = '<div class="notes-placeholder">Poznámky budú implementované v budúcej verzii</div>';
    }
}

// Close modal function (if not already defined)
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// Additional CRM Functions
function switchTasksView(viewType) {
    console.log('Switching tasks view to:', viewType);

    // Update view toggle buttons
    document.querySelectorAll('.tasks-view-toggle .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${viewType}"]`)?.classList.add('active');

    // Update view content
    document.querySelectorAll('.tasks-view-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${viewType}-view`)?.classList.add('active');

    // Load appropriate data
    if (viewType === 'kanban') {
        renderKanbanBoard();
    } else if (viewType === 'list') {
        renderTasksList();
    } else if (viewType === 'calendar') {
        renderTasksCalendar();
    }
}

function renderTasksList() {
    console.log('Rendering tasks list view');
    const container = document.getElementById('tasksListContainer');
    if (container) {
        container.innerHTML = '<div class="tasks-list-placeholder">Zoznamový pohľad úloh bude implementovaný v budúcej verzii</div>';
    }
}

function renderTasksCalendar() {
    console.log('Rendering tasks calendar view');
    // This would integrate with the main calendar view
}

// Export functions for global access
window.switchCRMView = switchCRMView;
window.showContactDetail = showContactDetail;
window.showTaskDetail = showTaskDetail;
window.showDealDetail = showDealDetail;
window.allowDrop = allowDrop;
window.dropTask = dropTask;
window.dropDeal = dropDeal;
window.editContact = editContact;
window.deleteContact = deleteContact;
window.editDeal = editDeal;
window.showAddContactModal = showAddContactModal;
window.showAddTaskModal = showAddTaskModal;
window.showAddDealModal = showAddDealModal;
window.handleAddContact = handleAddContact;
window.handleAddTask = handleAddTask;
window.handleAddDeal = handleAddDeal;
window.closeModal = closeModal;
window.switchTasksView = switchTasksView;
