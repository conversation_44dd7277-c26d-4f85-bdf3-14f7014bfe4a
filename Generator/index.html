<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>r<PERSON><PERSON> c<PERSON> - Starostlivosť o hrobové miesta</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- html2pdf.js Library - Local version for Electron with CDN fallback -->
    <script src="html2pdf.bundle.min.js"></script>
    <script>
        // Verify html2pdf is loaded with fallback to CDN
        window.addEventListener('load', function() {
            if (typeof window.html2pdf !== 'undefined') {
                console.log('html2pdf loaded successfully from local source');
            } else {
                console.warn('Local html2pdf failed to load, trying CDN fallback...');
                // Try loading from CDN as fallback
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.3/html2pdf.bundle.min.js';
                script.onload = function() {
                    if (typeof window.html2pdf !== 'undefined') {
                        console.log('html2pdf loaded successfully from CDN fallback');
                    } else {
                        console.error('html2pdf failed to load from both local and CDN sources');
                        alert('Chyba: Nepodarilo sa načítať html2pdf knižnicu. PDF generovanie nebude fungovať.');
                    }
                };
                script.onerror = function() {
                    console.error('html2pdf failed to load from CDN');
                    alert('Chyba: Nepodarilo sa načítať html2pdf knižnicu. PDF generovanie nebude fungovať.');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo-section">
                    <img src="https://storage.googleapis.com/espomienka/logo36.png" alt="eSpomienka Logo" class="logo-image">
                    <div class="logo-text">
                        <h1>eSpomienka</h1>
                        <span class="subtitle">Starostlivosť o hrobové miesta</span>
                    </div>
                </div>

                <!-- Main Navigation -->
                <nav class="main-nav">
                    <button class="nav-tab active" data-tab="quotes">
                        <i class="fas fa-file-invoice"></i> Cenová ponuka
                    </button>
                    <button class="nav-tab" data-tab="clients">
                        <i class="fas fa-users"></i> Klienti
                    </button>
                    <button class="nav-tab" data-tab="orders">
                        <i class="fas fa-tasks"></i> Objednávky
                    </button>
                    <button class="nav-tab" data-tab="invoices">
                        <i class="fas fa-file-invoice-dollar"></i> Faktúry
                    </button>
                    <button class="nav-tab" data-tab="services">
                        <i class="fas fa-cogs"></i> Služby
                    </button>
                    <button class="nav-tab" data-tab="settings">
                        <i class="fas fa-cog"></i> Nastavenia
                    </button>
                </nav>

                <div class="header-actions">
                    <!-- Header actions removed as requested -->
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- Tab Content Container -->
            <div class="tab-content">

                <!-- QUOTES TAB -->
                <div id="quotes-tab" class="tab-pane active">
                    <div class="quotes-layout">
                        <!-- Sidebar -->
                        <aside class="sidebar">
                            <nav class="service-nav">
                                <h3><i class="fas fa-list"></i> Kategórie služieb</h3>
                                <ul>
                                    <li><a href="#basic-services" class="nav-link active">
                                        <i class="fas fa-tools"></i> Základné služby
                                    </a></li>
                                    <li><a href="#packages" class="nav-link">
                                        <i class="fas fa-box"></i> Balíky služieb
                                    </a></li>
                                    <li><a href="#digital" class="nav-link">
                                        <i class="fas fa-qrcode"></i> Digitálne služby
                                    </a></li>
                                    <li><a href="#additional" class="nav-link">
                                        <i class="fas fa-plus"></i> Doplnkové služby
                                    </a></li>
                                    <li><a href="#special" class="nav-link">
                                        <i class="fas fa-star"></i> Špeciálne ponuky
                                    </a></li>
                                </ul>
                            </nav>
                        </aside>

            <!-- Main Form Area -->
            <main class="form-area">
                <!-- Customer Information -->
                <section class="customer-section">
                    <h2><i class="fas fa-user"></i> Údaje o zákazníkovi</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="customerName">Meno a priezvisko *</label>
                            <input type="text" id="customerName" required>
                        </div>
                        <div class="form-group">
                            <label for="customerPhone">Telefón *</label>
                            <input type="tel" id="customerPhone" required>
                        </div>
                        <div class="form-group">
                            <label for="customerEmail">Email *</label>
                            <input type="email" id="customerEmail" required>
                        </div>
                        <div class="form-group">
                            <label for="customerAddress">Adresa</label>
                            <input type="text" id="customerAddress">
                        </div>
                        <div class="form-group full-width">
                            <label for="cemetery">Cintorín/Lokalita</label>
                            <input type="text" id="cemetery">
                        </div>
                    </div>
                </section>

                <!-- Services Selection -->
                <section class="services-section">
                    <!-- Basic Services -->
                    <div id="basic-services" class="service-category active">
                        <h3><i class="fas fa-tools"></i> Základné služby</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>Základná údržba</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="17" data-service="Základná údržba - Urnové miesto">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Urnové miesto</span>
                                        <span class="service-price">17 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="29" data-service="Základná údržba - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob</span>
                                        <span class="service-price">29 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="40" data-service="Základná údržba - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob</span>
                                        <span class="service-price">40 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="52" data-service="Základná údržba - Trojhrob/Hrobka">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Trojhrob/Hrobka</span>
                                        <span class="service-price">od 52 EUR</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Hĺbkové čistenie</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="58" data-service="Hĺbkové čistenie - Urnové miesto">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Urnové miesto</span>
                                        <span class="service-price">58 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="115" data-service="Hĺbkové čistenie - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob</span>
                                        <span class="service-price">115 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="161" data-service="Hĺbkové čistenie - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob</span>
                                        <span class="service-price">161 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="207" data-service="Hĺbkové čistenie - Trojhrob/Hrobka">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Trojhrob/Hrobka</span>
                                        <span class="service-price">od 207 EUR</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Package Services -->
                    <div id="packages" class="service-category">
                        <h3><i class="fas fa-box"></i> Balíky služieb</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>Balík Sviatočný</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="63" data-service="Balík Sviatočný - Urnové miesto">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Urnové miesto</span>
                                        <span class="service-price">63 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="104" data-service="Balík Sviatočný - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob</span>
                                        <span class="service-price">104 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="150" data-service="Balík Sviatočný - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob</span>
                                        <span class="service-price">150 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="196" data-service="Balík Sviatočný - Trojhrob/Hrobka">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Trojhrob/Hrobka</span>
                                        <span class="service-price">od 196 EUR/rok</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Balík Celoročný Premium</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="403" data-service="Balík Celoročný Premium - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob</span>
                                        <span class="service-price">403 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="518" data-service="Balík Celoročný Premium - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob</span>
                                        <span class="service-price">518 EUR/rok</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Digital Services -->
                    <div id="digital" class="service-category">
                        <h3><i class="fas fa-qrcode"></i> Digitálne služby - ESPOMIENKA</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>QR kód balíčky</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="35" data-service="QR kód - Základný balíček">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Základný balíček</span>
                                        <span class="service-price">35 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="65" data-service="QR kód - Rozšírený balíček">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Rozšírený balíček</span>
                                        <span class="service-price">65 EUR/3 roky</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="95" data-service="QR kód - Rodinný balíček">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Rodinný balíček</span>
                                        <span class="service-price">95 EUR/5 rokov</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="0" data-service="QR kód - Riešenie na mieru">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Riešenie na mieru</span>
                                        <span class="service-price">cena na vyžiadanie</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Montáž QR kódu</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="5" data-service="Montáž QR kódu - Prilepenie">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Prilepenie</span>
                                        <span class="service-price">5 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="25" data-service="Montáž QR kódu - Vyleptanie">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Vyleptanie</span>
                                        <span class="service-price">od 25 EUR</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Services -->
                    <div id="additional" class="service-category">
                        <h3><i class="fas fa-plus"></i> Doplnkové služby</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>Písmo a nápisy</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="1.73" data-service="Obnova farby písma" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Obnova farby písma</span>
                                        <span class="service-price">1,73 EUR/znak</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Počet znakov:</label>
                                        <input type="number" min="1" data-multiplier="1.73" placeholder="0">
                                    </div>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="11.50" data-service="Dosekanie nového písma" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dosekanie nového písma</span>
                                        <span class="service-price">11,50 EUR/znak</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Počet znakov:</label>
                                        <input type="number" min="1" data-multiplier="11.50" placeholder="0">
                                    </div>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Výsadba a materiály</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="11.50" data-service="Sezónna výsadba kvetov" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Sezónna výsadba kvetov</span>
                                        <span class="service-price">cena kvetov + 11,50 EUR</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Cena kvetov (EUR):</label>
                                        <input type="number" min="0" step="0.01" data-additional="11.50" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="17.25" data-service="Dosypanie kameniva/kôry" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dosypanie kameniva/kôry</span>
                                        <span class="service-price">cena materiálu + 17,25 EUR</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Cena materiálu (EUR):</label>
                                        <input type="number" min="0" step="0.01" data-additional="17.25" placeholder="0.00">
                                    </div>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Ostatné služby</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="23" data-service="Samostatná impregnácia - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Samostatná impregnácia - Jednohrob</span>
                                        <span class="service-price">23 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="40" data-service="Samostatná impregnácia - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Samostatná impregnácia - Dvojhrob</span>
                                        <span class="service-price">40 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="11.50" data-service="Donáška a aranžovanie" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Donáška a aranžovanie</span>
                                        <span class="service-price">cena tovaru + 11,50 EUR</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Cena tovaru (EUR):</label>
                                        <input type="number" min="0" step="0.01" data-additional="11.50" placeholder="0.00">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Special Offers -->
                    <div id="special" class="service-category">
                        <h3><i class="fas fa-star"></i> Špeciálne ponuky</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>Akcia 2+1 ZADARMO - Balík Sviatočný - URNOVÉ MIESTA</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="90" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Malé urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Malé urnové miesto (3 roky)</span>
                                        <span class="service-price">90 EUR (normálne 135 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="126" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Veľké urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Veľké urnové miesto (3 roky)</span>
                                        <span class="service-price">126 EUR (normálne 189 EUR)</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Akcia 2+1 ZADARMO - Balík Sviatočný - HROBY</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="126" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Urnové miesto (3 roky)</span>
                                        <span class="service-price">126 EUR (ušetríte 63 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="208" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Jednohrob" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob (3 roky)</span>
                                        <span class="service-price">208 EUR (ušetríte 104 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="300" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Dvojhrob" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob (3 roky)</span>
                                        <span class="service-price">300 EUR (ušetríte 150 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="392" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Trojhrob/Hrobka" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Trojhrob/Hrobka (3 roky)</span>
                                        <span class="service-price">od 392 EUR (ušetríte 196 EUR)</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Akcia 2+1 ZADARMO - Balík Celoročný Premium - URNOVÉ MIESTA</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="354" data-service="Akcia 2+1 ZADARMO - Balík Celoročný Premium Malé urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Malé urnové miesto (3 roky)</span>
                                        <span class="service-price">354 EUR (normálne 531 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="490" data-service="Akcia 2+1 ZADARMO - Balík Celoročný Premium Veľké urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Veľké urnové miesto (3 roky)</span>
                                        <span class="service-price">490 EUR (normálne 735 EUR)</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Akcia 2+1 ZADARMO - Balík Celoročný Premium - HROBY</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="806" data-service="Akcia 2+1 ZADARMO - Balík Celoročný Premium Jednohrob" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob (3 roky)</span>
                                        <span class="service-price">806 EUR (ušetríte 403 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="1036" data-service="Akcia 2+1 ZADARMO - Balík Celoročný Premium Dvojhrob" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob (3 roky)</span>
                                        <span class="service-price">1036 EUR (ušetríte 518 EUR)</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>

                        <!-- Calculator Sidebar -->
                        <aside class="calculator">
                            <div class="calculator-content">
                                <h3><i class="fas fa-calculator"></i> Kalkulácia</h3>
                                <div class="selected-services">
                                    <h4>Vybrané služby:</h4>
                                    <div id="selectedServicesList" class="services-list">
                                        <p class="no-services">Žiadne služby nie sú vybrané</p>
                                    </div>
                                </div>
                                <div class="discount-section">
                                    <h4>Zľava:</h4>
                                    <div class="discount-options">
                                        <label class="discount-option">
                                            <input type="radio" name="discount" value="0" checked>
                                            <span>Bez zľavy</span>
                                        </label>
                                        <label class="discount-option">
                                            <input type="radio" name="discount" value="5">
                                            <span>5%</span>
                                        </label>
                                        <label class="discount-option">
                                            <input type="radio" name="discount" value="10">
                                            <span>10%</span>
                                        </label>
                                        <label class="discount-option">
                                            <input type="radio" name="discount" value="15">
                                            <span>15%</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="price-summary">
                                    <div class="price-row">
                                        <span>Súčet bez DPH:</span>
                                        <span id="subtotal">0,00 EUR</span>
                                    </div>
                                    <div class="price-row discount-row" id="discountRow" style="display: none;">
                                        <span>Zľava (<span id="discountPercent">0</span>%):</span>
                                        <span id="discountAmount">-0,00 EUR</span>
                                    </div>
                                    <div class="price-row">
                                        <span>Súčet po zľave:</span>
                                        <span id="subtotalAfterDiscount">0,00 EUR</span>
                                    </div>
                                    <div class="price-row">
                                        <span>DPH 20%:</span>
                                        <span id="vat">0,00 EUR</span>
                                    </div>
                                    <div class="price-row total">
                                        <span>Celkom s DPH:</span>
                                        <span id="total">0,00 EUR</span>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-generate" onclick="generatePDF()">
                                    <i class="fas fa-file-pdf"></i> Generovať PDF ponuku
                                </button>
                                <button class="btn btn-primary" onclick="createOrderFromQuote()" style="margin-top: 10px;">
                                    <i class="fas fa-plus"></i> Vytvoriť objednávku
                                </button>
                            </div>
                        </aside>
                    </div>
                </div>

                <!-- CLIENTS TAB -->
                <div id="clients-tab" class="tab-pane">
                    <div class="clients-layout">
                        <!-- Clients Header -->
                        <div class="clients-header">
                            <h2><i class="fas fa-users"></i> Správa klientov</h2>
                            <div class="clients-actions">
                                <button class="btn btn-primary" onclick="showAddClientModal()">
                                    <i class="fas fa-plus"></i> Nový klient
                                </button>
                                <button class="btn btn-secondary" onclick="exportClients()">
                                    <i class="fas fa-download"></i> Export CSV
                                </button>
                            </div>
                        </div>

                        <!-- Clients Stats -->
                        <div class="clients-stats">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-users"></i></div>
                                <div class="stat-content">
                                    <h3 id="totalClients">0</h3>
                                    <p>Celkom klientov</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-euro-sign"></i></div>
                                <div class="stat-content">
                                    <h3 id="totalClientRevenue">0,00 €</h3>
                                    <p>Celkové tržby</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-file-contract"></i></div>
                                <div class="stat-content">
                                    <h3 id="activeClientContracts">0</h3>
                                    <p>Aktívne zmluvy</p>
                                </div>
                            </div>
                        </div>

                        <!-- Clients Search and Filter -->
                        <div class="clients-filters">
                            <div class="filter-group">
                                <label>Hľadať:</label>
                                <input type="text" id="clientSearch" placeholder="Meno, telefón, email...">
                            </div>
                            <div class="filter-group">
                                <label>Zoradiť podľa:</label>
                                <select id="clientSort">
                                    <option value="name">Meno</option>
                                    <option value="revenue">Tržby</option>
                                    <option value="created">Dátum vytvorenia</option>
                                </select>
                            </div>
                        </div>

                        <!-- Clients List -->
                        <div class="clients-list" id="clientsList">
                            <!-- Clients will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- ORDERS TAB - ENHANCED CRM SYSTEM -->
                <div id="orders-tab" class="tab-pane">
                    <!-- CRM Navigation -->
                    <div class="crm-navigation">
                        <div class="crm-nav-tabs">
                            <button class="crm-nav-tab active" data-crm-view="dashboard">
                                <i class="fas fa-chart-bar"></i> Dashboard
                            </button>
                            <button class="crm-nav-tab" data-crm-view="contacts">
                                <i class="fas fa-users"></i> Kontakty
                            </button>
                            <button class="crm-nav-tab" data-crm-view="tasks">
                                <i class="fas fa-tasks"></i> Úlohy
                            </button>
                            <button class="crm-nav-tab" data-crm-view="pipeline">
                                <i class="fas fa-chart-line"></i> Pipeline
                            </button>
                            <button class="crm-nav-tab" data-crm-view="calendar">
                                <i class="fas fa-calendar"></i> Kalendár
                            </button>
                            <button class="crm-nav-tab" data-crm-view="notes">
                                <i class="fas fa-sticky-note"></i> Poznámky
                            </button>
                        </div>

                        <!-- Global Search -->
                        <div class="global-search">
                            <div class="search-container">
                                <i class="fas fa-search"></i>
                                <input type="text" id="globalSearch" placeholder="Globálne vyhľadávanie... (Ctrl+K)">
                                <div class="search-suggestions" id="searchSuggestions"></div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="showAddOrderModal()">
                                <i class="fas fa-plus"></i> Nová objednávka
                            </button>
                            <button class="btn btn-secondary" onclick="showAddContactModal()">
                                <i class="fas fa-user-plus"></i> Nový kontakt
                            </button>
                        </div>
                    </div>

                    <!-- CRM DASHBOARD VIEW -->
                    <div id="crm-dashboard" class="crm-view active">
                        <div class="dashboard-layout">
                            <!-- Enhanced Metrics Cards -->
                            <div class="dashboard-section">
                                <h2><i class="fas fa-chart-bar"></i> Prehľad výkonnosti</h2>
                                <div class="dashboard-cards">
                                    <div class="dashboard-card metric-card">
                                        <div class="card-icon"><i class="fas fa-users"></i></div>
                                        <div class="card-content">
                                            <h3 id="totalContacts">0</h3>
                                            <p>Celkový počet kontaktov</p>
                                            <div class="trend-indicator">
                                                <span class="trend-value" id="contactsTrend">+0</span>
                                                <span class="trend-period">tento mesiac</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="dashboard-card metric-card">
                                        <div class="card-icon"><i class="fas fa-tasks"></i></div>
                                        <div class="card-content">
                                            <h3 id="activeTasks">0</h3>
                                            <p>Aktívne úlohy</p>
                                            <div class="priority-breakdown">
                                                <span class="priority-high" id="highPriorityTasks">0</span>
                                                <span class="priority-medium" id="mediumPriorityTasks">0</span>
                                                <span class="priority-low" id="lowPriorityTasks">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="dashboard-card metric-card">
                                        <div class="card-icon"><i class="fas fa-check-circle"></i></div>
                                        <div class="card-content">
                                            <h3 id="completedThisWeek">0</h3>
                                            <p>Dokončené tento týždeň</p>
                                            <div class="completion-rate">
                                                <span id="completionRate">0%</span> úspešnosť
                                            </div>
                                        </div>
                                    </div>
                                    <div class="dashboard-card metric-card">
                                        <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                                        <div class="card-content">
                                            <h3 id="conversionRate">0%</h3>
                                            <p>Conversion rate</p>
                                            <div class="conversion-details">
                                                Leads → Zákazníci
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Kanban Board Preview -->
                            <div class="dashboard-section">
                                <div class="section-header">
                                    <h3><i class="fas fa-columns"></i> Najdôležitejšie úlohy</h3>
                                    <button class="btn btn-secondary btn-sm" onclick="switchCRMView('tasks')">
                                        Zobraziť všetky
                                    </button>
                                </div>
                                <div class="mini-kanban">
                                    <div class="kanban-column">
                                        <h4><i class="fas fa-inbox"></i> Nové</h4>
                                        <div id="miniKanbanNew" class="mini-kanban-tasks"></div>
                                    </div>
                                    <div class="kanban-column">
                                        <h4><i class="fas fa-play"></i> V riešení</h4>
                                        <div id="miniKanbanProgress" class="mini-kanban-tasks"></div>
                                    </div>
                                    <div class="kanban-column">
                                        <h4><i class="fas fa-eye"></i> Na kontrole</h4>
                                        <div id="miniKanbanReview" class="mini-kanban-tasks"></div>
                                    </div>
                                    <div class="kanban-column">
                                        <h4><i class="fas fa-check"></i> Dokončené</h4>
                                        <div id="miniKanbanDone" class="mini-kanban-tasks"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Upcoming Deadlines -->
                            <div class="dashboard-section">
                                <h3><i class="fas fa-clock"></i> Nadchádzajúce termíny (7 dní)</h3>
                                <div id="upcomingDeadlines" class="upcoming-deadlines">
                                    <!-- Populated by JavaScript -->
                                </div>
                            </div>

                            <!-- Activity Feed -->
                            <div class="dashboard-section">
                                <h3><i class="fas fa-history"></i> Posledná aktivita</h3>
                                <div id="activityFeed" class="activity-feed">
                                    <!-- Populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CRM CONTACTS VIEW -->
                    <div id="crm-contacts" class="crm-view">
                        <div class="contacts-layout">
                            <!-- Contacts Header -->
                            <div class="contacts-header">
                                <h2><i class="fas fa-users"></i> Správa kontaktov</h2>
                                <div class="contacts-actions">
                                    <button class="btn btn-primary" onclick="showAddContactModal()">
                                        <i class="fas fa-user-plus"></i> Pridať kontakt
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportContacts()">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                    <button class="btn btn-secondary" onclick="importContacts()">
                                        <i class="fas fa-upload"></i> Import
                                    </button>
                                </div>
                            </div>

                            <!-- Contacts Filters -->
                            <div class="contacts-filters">
                                <div class="filter-group">
                                    <label>Vyhľadávanie:</label>
                                    <input type="text" id="contactsSearch" placeholder="Meno, email, spoločnosť...">
                                </div>
                                <div class="filter-group">
                                    <label>Status:</label>
                                    <select id="contactStatusFilter">
                                        <option value="all">Všetky</option>
                                        <option value="lead">Lead</option>
                                        <option value="prospect">Prospect</option>
                                        <option value="customer">Zákazník</option>
                                        <option value="inactive">Neaktívny</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>Pridelený užívateľ:</label>
                                    <select id="assignedUserFilter">
                                        <option value="all">Všetci</option>
                                        <option value="me">Moje kontakty</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>Dátum vytvorenia:</label>
                                    <select id="dateCreatedFilter">
                                        <option value="all">Všetky</option>
                                        <option value="today">Dnes</option>
                                        <option value="week">Tento týždeň</option>
                                        <option value="month">Tento mesiac</option>
                                    </select>
                                </div>
                                <div class="filter-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="clearContactFilters()">
                                        <i class="fas fa-times"></i> Vymazať filtre
                                    </button>
                                </div>
                            </div>

                            <!-- Contacts Table -->
                            <div class="contacts-table-container">
                                <div class="table-controls">
                                    <div class="bulk-actions">
                                        <input type="checkbox" id="selectAllContacts" onchange="toggleSelectAllContacts()">
                                        <label for="selectAllContacts">Vybrať všetky</label>
                                        <button class="btn btn-secondary btn-sm" onclick="bulkDeleteContacts()" disabled>
                                            <i class="fas fa-trash"></i> Vymazať vybrané
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="bulkExportContacts()" disabled>
                                            <i class="fas fa-download"></i> Export vybraných
                                        </button>
                                    </div>
                                    <div class="table-info">
                                        <span id="contactsCount">0 kontaktov</span>
                                    </div>
                                </div>

                                <table class="contacts-table">
                                    <thead>
                                        <tr>
                                            <th width="40px"></th>
                                            <th onclick="sortContacts('name')" class="sortable">
                                                <i class="fas fa-user"></i> Meno
                                                <i class="fas fa-sort sort-icon"></i>
                                            </th>
                                            <th onclick="sortContacts('company')" class="sortable">
                                                <i class="fas fa-building"></i> Spoločnosť
                                                <i class="fas fa-sort sort-icon"></i>
                                            </th>
                                            <th onclick="sortContacts('email')" class="sortable">
                                                <i class="fas fa-envelope"></i> Email
                                                <i class="fas fa-sort sort-icon"></i>
                                            </th>
                                            <th onclick="sortContacts('phone')" class="sortable">
                                                <i class="fas fa-phone"></i> Telefón
                                                <i class="fas fa-sort sort-icon"></i>
                                            </th>
                                            <th onclick="sortContacts('status')" class="sortable">
                                                <i class="fas fa-flag"></i> Status
                                                <i class="fas fa-sort sort-icon"></i>
                                            </th>
                                            <th onclick="sortContacts('lastContact')" class="sortable">
                                                <i class="fas fa-clock"></i> Posledný kontakt
                                                <i class="fas fa-sort sort-icon"></i>
                                            </th>
                                            <th onclick="sortContacts('dealValue')" class="sortable">
                                                <i class="fas fa-euro-sign"></i> Hodnota
                                                <i class="fas fa-sort sort-icon"></i>
                                            </th>
                                            <th width="120px">Akcie</th>
                                        </tr>
                                    </thead>
                                    <tbody id="contactsTableBody">
                                        <!-- Populated by JavaScript -->
                                    </tbody>
                                </table>

                                <div class="table-pagination">
                                    <div class="pagination-info">
                                        Zobrazuje sa <span id="paginationStart">0</span> - <span id="paginationEnd">0</span> z <span id="paginationTotal">0</span>
                                    </div>
                                    <div class="pagination-controls">
                                        <button class="btn btn-secondary btn-sm" onclick="previousPage()" disabled>
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                        <span id="currentPage">1</span> / <span id="totalPages">1</span>
                                        <button class="btn btn-secondary btn-sm" onclick="nextPage()" disabled>
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CRM TASKS VIEW -->
                    <div id="crm-tasks" class="crm-view">
                        <div class="tasks-layout">
                            <!-- Tasks Header -->
                            <div class="tasks-header">
                                <h2><i class="fas fa-tasks"></i> Správa úloh</h2>
                                <div class="tasks-view-toggle">
                                    <button class="btn btn-secondary btn-sm active" data-view="kanban" onclick="switchTasksView('kanban')">
                                        <i class="fas fa-columns"></i> Kanban
                                    </button>
                                    <button class="btn btn-secondary btn-sm" data-view="list" onclick="switchTasksView('list')">
                                        <i class="fas fa-list"></i> Zoznam
                                    </button>
                                    <button class="btn btn-secondary btn-sm" data-view="calendar" onclick="switchTasksView('calendar')">
                                        <i class="fas fa-calendar"></i> Kalendár
                                    </button>
                                </div>
                                <div class="tasks-actions">
                                    <button class="btn btn-primary" onclick="showAddTaskModal()">
                                        <i class="fas fa-plus"></i> Nová úloha
                                    </button>
                                    <button class="btn btn-secondary" onclick="showBulkTaskActions()">
                                        <i class="fas fa-cogs"></i> Hromadné akcie
                                    </button>
                                </div>
                            </div>

                            <!-- Tasks Filters -->
                            <div class="tasks-filters">
                                <div class="filter-group">
                                    <label>Vyhľadávanie:</label>
                                    <input type="text" id="tasksSearch" placeholder="Názov úlohy, zákazník...">
                                </div>
                                <div class="filter-group">
                                    <label>Pridelený užívateľ:</label>
                                    <select id="taskAssigneeFilter">
                                        <option value="all">Všetci</option>
                                        <option value="me">Moje úlohy</option>
                                        <option value="unassigned">Nepridelené</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>Priorita:</label>
                                    <select id="taskPriorityFilter">
                                        <option value="all">Všetky</option>
                                        <option value="high">Vysoká</option>
                                        <option value="medium">Stredná</option>
                                        <option value="low">Nízka</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>Termín:</label>
                                    <select id="taskDeadlineFilter">
                                        <option value="all">Všetky</option>
                                        <option value="overdue">Po termíne</option>
                                        <option value="today">Dnes</option>
                                        <option value="week">Tento týždeň</option>
                                        <option value="month">Tento mesiac</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>Značky:</label>
                                    <select id="taskLabelsFilter">
                                        <option value="all">Všetky</option>
                                        <option value="urgent">Urgentné</option>
                                        <option value="cleaning">Čistenie</option>
                                        <option value="maintenance">Údržba</option>
                                        <option value="follow-up">Follow-up</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Kanban Board View -->
                            <div id="kanban-view" class="tasks-view-content active">
                                <div class="kanban-board">
                                    <div class="kanban-column" data-status="new">
                                        <div class="kanban-header">
                                            <h3><i class="fas fa-inbox"></i> Nové</h3>
                                            <span class="task-count" id="newTasksCount">0</span>
                                        </div>
                                        <div class="kanban-tasks" id="kanbanNew" ondrop="dropTask(event)" ondragover="allowDrop(event)">
                                            <!-- Tasks populated by JavaScript -->
                                        </div>
                                        <button class="add-task-btn" onclick="showAddTaskModal('new')">
                                            <i class="fas fa-plus"></i> Pridať úlohu
                                        </button>
                                    </div>

                                    <div class="kanban-column" data-status="in_progress">
                                        <div class="kanban-header">
                                            <h3><i class="fas fa-play"></i> V riešení</h3>
                                            <span class="task-count" id="inProgressTasksCount">0</span>
                                        </div>
                                        <div class="kanban-tasks" id="kanbanInProgress" ondrop="dropTask(event)" ondragover="allowDrop(event)">
                                            <!-- Tasks populated by JavaScript -->
                                        </div>
                                        <button class="add-task-btn" onclick="showAddTaskModal('in_progress')">
                                            <i class="fas fa-plus"></i> Pridať úlohu
                                        </button>
                                    </div>

                                    <div class="kanban-column" data-status="review">
                                        <div class="kanban-header">
                                            <h3><i class="fas fa-eye"></i> Na kontrole</h3>
                                            <span class="task-count" id="reviewTasksCount">0</span>
                                        </div>
                                        <div class="kanban-tasks" id="kanbanReview" ondrop="dropTask(event)" ondragover="allowDrop(event)">
                                            <!-- Tasks populated by JavaScript -->
                                        </div>
                                        <button class="add-task-btn" onclick="showAddTaskModal('review')">
                                            <i class="fas fa-plus"></i> Pridať úlohu
                                        </button>
                                    </div>

                                    <div class="kanban-column" data-status="done">
                                        <div class="kanban-header">
                                            <h3><i class="fas fa-check"></i> Dokončené</h3>
                                            <span class="task-count" id="doneTasksCount">0</span>
                                        </div>
                                        <div class="kanban-tasks" id="kanbanDone" ondrop="dropTask(event)" ondragover="allowDrop(event)">
                                            <!-- Tasks populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- List View -->
                            <div id="list-view" class="tasks-view-content">
                                <div class="tasks-list-container">
                                    <div class="list-controls">
                                        <div class="grouping-options">
                                            <label>Zoskupiť podľa:</label>
                                            <select id="taskGrouping">
                                                <option value="none">Bez zoskupenia</option>
                                                <option value="assignee">Užívateľ</option>
                                                <option value="priority">Priorita</option>
                                                <option value="deadline">Termín</option>
                                                <option value="project">Projekt</option>
                                            </select>
                                        </div>
                                        <div class="bulk-task-actions">
                                            <input type="checkbox" id="selectAllTasks" onchange="toggleSelectAllTasks()">
                                            <label for="selectAllTasks">Vybrať všetky</label>
                                            <button class="btn btn-secondary btn-sm" onclick="bulkChangeStatus()" disabled>
                                                Zmeniť status
                                            </button>
                                            <button class="btn btn-secondary btn-sm" onclick="bulkAssignTasks()" disabled>
                                                Prideliť
                                            </button>
                                            <button class="btn btn-secondary btn-sm" onclick="bulkDeleteTasks()" disabled>
                                                Vymazať
                                            </button>
                                        </div>
                                    </div>
                                    <div id="tasksListContainer" class="tasks-list-content">
                                        <!-- Tasks populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CRM PIPELINE VIEW -->
                    <div id="crm-pipeline" class="crm-view">
                        <div class="pipeline-layout">
                            <!-- Pipeline Header -->
                            <div class="pipeline-header">
                                <h2><i class="fas fa-chart-line"></i> Pipeline management</h2>
                                <div class="pipeline-stats">
                                    <div class="stat-item">
                                        <span class="stat-value" id="totalDealsValue">0,00 €</span>
                                        <span class="stat-label">Celková hodnota</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value" id="averageDealValue">0,00 €</span>
                                        <span class="stat-label">Priemerná hodnota</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value" id="conversionRatePipeline">0%</span>
                                        <span class="stat-label">Conversion rate</span>
                                    </div>
                                </div>
                                <div class="pipeline-actions">
                                    <button class="btn btn-primary" onclick="showAddDealModal()">
                                        <i class="fas fa-plus"></i> Nová príležitosť
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportPipeline()">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                </div>
                            </div>

                            <!-- Pipeline Filters -->
                            <div class="pipeline-filters">
                                <div class="filter-group">
                                    <label>Vyhľadávanie:</label>
                                    <input type="text" id="pipelineSearch" placeholder="Názov príležitosti, zákazník...">
                                </div>
                                <div class="filter-group">
                                    <label>Hodnota od:</label>
                                    <input type="number" id="minDealValue" placeholder="0">
                                </div>
                                <div class="filter-group">
                                    <label>Hodnota do:</label>
                                    <input type="number" id="maxDealValue" placeholder="10000">
                                </div>
                                <div class="filter-group">
                                    <label>Očakávaný termín:</label>
                                    <select id="expectedCloseFilter">
                                        <option value="all">Všetky</option>
                                        <option value="overdue">Po termíne</option>
                                        <option value="thisWeek">Tento týždeň</option>
                                        <option value="thisMonth">Tento mesiac</option>
                                        <option value="nextMonth">Budúci mesiac</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Pipeline Board -->
                            <div class="pipeline-board">
                                <div class="pipeline-stage" data-stage="lead">
                                    <div class="stage-header">
                                        <h3><i class="fas fa-bullseye"></i> Lead</h3>
                                        <div class="stage-stats">
                                            <span class="deals-count" id="leadDealsCount">0</span>
                                            <span class="stage-value" id="leadStageValue">0,00 €</span>
                                        </div>
                                    </div>
                                    <div class="stage-deals" id="leadDeals" ondrop="dropDeal(event)" ondragover="allowDrop(event)">
                                        <!-- Deals populated by JavaScript -->
                                    </div>
                                    <button class="add-deal-btn" onclick="showAddDealModal('lead')">
                                        <i class="fas fa-plus"></i> Pridať príležitosť
                                    </button>
                                </div>

                                <div class="pipeline-stage" data-stage="qualified">
                                    <div class="stage-header">
                                        <h3><i class="fas fa-handshake"></i> Kvalifikovaný</h3>
                                        <div class="stage-stats">
                                            <span class="deals-count" id="qualifiedDealsCount">0</span>
                                            <span class="stage-value" id="qualifiedStageValue">0,00 €</span>
                                        </div>
                                    </div>
                                    <div class="stage-deals" id="qualifiedDeals" ondrop="dropDeal(event)" ondragover="allowDrop(event)">
                                        <!-- Deals populated by JavaScript -->
                                    </div>
                                    <button class="add-deal-btn" onclick="showAddDealModal('qualified')">
                                        <i class="fas fa-plus"></i> Pridať príležitosť
                                    </button>
                                </div>

                                <div class="pipeline-stage" data-stage="presentation">
                                    <div class="stage-header">
                                        <h3><i class="fas fa-presentation"></i> Prezentácia</h3>
                                        <div class="stage-stats">
                                            <span class="deals-count" id="presentationDealsCount">0</span>
                                            <span class="stage-value" id="presentationStageValue">0,00 €</span>
                                        </div>
                                    </div>
                                    <div class="stage-deals" id="presentationDeals" ondrop="dropDeal(event)" ondragover="allowDrop(event)">
                                        <!-- Deals populated by JavaScript -->
                                    </div>
                                    <button class="add-deal-btn" onclick="showAddDealModal('presentation')">
                                        <i class="fas fa-plus"></i> Pridať príležitosť
                                    </button>
                                </div>

                                <div class="pipeline-stage" data-stage="proposal">
                                    <div class="stage-header">
                                        <h3><i class="fas fa-file-contract"></i> Návrh</h3>
                                        <div class="stage-stats">
                                            <span class="deals-count" id="proposalDealsCount">0</span>
                                            <span class="stage-value" id="proposalStageValue">0,00 €</span>
                                        </div>
                                    </div>
                                    <div class="stage-deals" id="proposalDeals" ondrop="dropDeal(event)" ondragover="allowDrop(event)">
                                        <!-- Deals populated by JavaScript -->
                                    </div>
                                    <button class="add-deal-btn" onclick="showAddDealModal('proposal')">
                                        <i class="fas fa-plus"></i> Pridať príležitosť
                                    </button>
                                </div>

                                <div class="pipeline-stage" data-stage="negotiation">
                                    <div class="stage-header">
                                        <h3><i class="fas fa-comments"></i> Rokovanie</h3>
                                        <div class="stage-stats">
                                            <span class="deals-count" id="negotiationDealsCount">0</span>
                                            <span class="stage-value" id="negotiationStageValue">0,00 €</span>
                                        </div>
                                    </div>
                                    <div class="stage-deals" id="negotiationDeals" ondrop="dropDeal(event)" ondragover="allowDrop(event)">
                                        <!-- Deals populated by JavaScript -->
                                    </div>
                                    <button class="add-deal-btn" onclick="showAddDealModal('negotiation')">
                                        <i class="fas fa-plus"></i> Pridať príležitosť
                                    </button>
                                </div>

                                <div class="pipeline-stage" data-stage="closed">
                                    <div class="stage-header">
                                        <h3><i class="fas fa-check-circle"></i> Zatvorené</h3>
                                        <div class="stage-stats">
                                            <span class="deals-count" id="closedDealsCount">0</span>
                                            <span class="stage-value" id="closedStageValue">0,00 €</span>
                                        </div>
                                    </div>
                                    <div class="stage-deals" id="closedDeals" ondrop="dropDeal(event)" ondragover="allowDrop(event)">
                                        <!-- Deals populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CRM CALENDAR VIEW -->
                    <div id="crm-calendar" class="crm-view">
                        <div class="calendar-layout">
                            <!-- Calendar Header -->
                            <div class="calendar-header">
                                <h2><i class="fas fa-calendar"></i> Kalendár udalostí</h2>
                                <div class="calendar-controls">
                                    <button class="btn btn-secondary" onclick="previousMonth()">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <h3 id="currentMonthYear">Január 2025</h3>
                                    <button class="btn btn-secondary" onclick="nextMonth()">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                    <button class="btn btn-secondary" onclick="goToToday()">
                                        Dnes
                                    </button>
                                </div>
                                <div class="calendar-view-options">
                                    <button class="btn btn-secondary btn-sm active" data-view="month" onclick="switchCalendarView('month')">
                                        Mesiac
                                    </button>
                                    <button class="btn btn-secondary btn-sm" data-view="week" onclick="switchCalendarView('week')">
                                        Týždeň
                                    </button>
                                    <button class="btn btn-secondary btn-sm" data-view="day" onclick="switchCalendarView('day')">
                                        Deň
                                    </button>
                                    <button class="btn btn-secondary btn-sm" data-view="agenda" onclick="switchCalendarView('agenda')">
                                        Agenda
                                    </button>
                                </div>
                                <div class="calendar-actions">
                                    <button class="btn btn-primary" onclick="showAddEventModal()">
                                        <i class="fas fa-plus"></i> Nová udalosť
                                    </button>
                                </div>
                            </div>

                            <!-- Calendar Filters -->
                            <div class="calendar-filters">
                                <div class="event-types">
                                    <label>Typy udalostí:</label>
                                    <div class="event-type-checkboxes">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="showMeetings" checked onchange="filterCalendarEvents()">
                                            <span class="event-color meetings"></span> Stretnutia
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="showDeadlines" checked onchange="filterCalendarEvents()">
                                            <span class="event-color deadlines"></span> Termíny úloh
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="showFollowUps" checked onchange="filterCalendarEvents()">
                                            <span class="event-color follow-ups"></span> Follow-up hovory
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="showPresentations" checked onchange="filterCalendarEvents()">
                                            <span class="event-color presentations"></span> Prezentácie
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="showProjects" checked onchange="filterCalendarEvents()">
                                            <span class="event-color projects"></span> Deadline projektu
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Calendar Grid -->
                            <div id="calendar-month-view" class="calendar-view-content active">
                                <div class="calendar-grid">
                                    <div class="calendar-weekdays">
                                        <div class="weekday">Po</div>
                                        <div class="weekday">Ut</div>
                                        <div class="weekday">St</div>
                                        <div class="weekday">Št</div>
                                        <div class="weekday">Pi</div>
                                        <div class="weekday">So</div>
                                        <div class="weekday">Ne</div>
                                    </div>
                                    <div id="calendarDays" class="calendar-days">
                                        <!-- Calendar days populated by JavaScript -->
                                    </div>
                                </div>
                            </div>

                            <!-- Week View -->
                            <div id="calendar-week-view" class="calendar-view-content">
                                <div class="week-view-container">
                                    <div class="time-slots">
                                        <!-- Time slots from 6:00 to 22:00 -->
                                    </div>
                                    <div class="week-days" id="weekDays">
                                        <!-- Week days populated by JavaScript -->
                                    </div>
                                </div>
                            </div>

                            <!-- Day View -->
                            <div id="calendar-day-view" class="calendar-view-content">
                                <div class="day-view-container">
                                    <div class="day-header">
                                        <h3 id="selectedDayTitle">Dnes</h3>
                                    </div>
                                    <div class="day-schedule" id="daySchedule">
                                        <!-- Day schedule populated by JavaScript -->
                                    </div>
                                </div>
                            </div>

                            <!-- Agenda View -->
                            <div id="calendar-agenda-view" class="calendar-view-content">
                                <div class="agenda-container">
                                    <div class="agenda-filters">
                                        <select id="agendaPeriod">
                                            <option value="week">Tento týždeň</option>
                                            <option value="month">Tento mesiac</option>
                                            <option value="quarter">Tento štvrťrok</option>
                                        </select>
                                    </div>
                                    <div id="agendaList" class="agenda-list">
                                        <!-- Agenda items populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CRM NOTES VIEW -->
                    <div id="crm-notes" class="crm-view">
                        <div class="notes-layout">
                            <!-- Notes Header -->
                            <div class="notes-header">
                                <h2><i class="fas fa-sticky-note"></i> Poznámky</h2>
                                <div class="notes-actions">
                                    <button class="btn btn-primary" onclick="showAddNoteModal()">
                                        <i class="fas fa-plus"></i> Nová poznámka
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportNotes()">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                </div>
                            </div>

                            <!-- Notes Filters -->
                            <div class="notes-filters">
                                <div class="filter-group">
                                    <label>Vyhľadávanie:</label>
                                    <input type="text" id="notesSearch" placeholder="Hľadať v poznámkach...">
                                </div>
                                <div class="filter-group">
                                    <label>Kategória:</label>
                                    <select id="notesCategoryFilter">
                                        <option value="all">Všetky</option>
                                        <option value="meeting">Stretnutie</option>
                                        <option value="call">Hovor</option>
                                        <option value="idea">Nápad</option>
                                        <option value="task">Úloha</option>
                                        <option value="project">Projekt</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>Dátum:</label>
                                    <select id="notesDateFilter">
                                        <option value="all">Všetky</option>
                                        <option value="today">Dnes</option>
                                        <option value="week">Tento týždeň</option>
                                        <option value="month">Tento mesiac</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>Značky:</label>
                                    <input type="text" id="notesTagsFilter" placeholder="Zadajte značky...">
                                </div>
                            </div>

                            <!-- Notes Grid -->
                            <div class="notes-grid" id="notesGrid">
                                <!-- Notes populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- INVOICES TAB -->
                <div id="invoices-tab" class="tab-pane">
                    <div class="invoices-layout">
                        <!-- Invoices Header -->
                        <div class="invoices-header">
                            <h2><i class="fas fa-file-invoice-dollar"></i> Správa faktúr</h2>
                            <div class="invoices-actions">
                                <button class="btn btn-primary" onclick="showAddInvoiceModal()">
                                    <i class="fas fa-plus"></i> Nová faktúra
                                </button>
                                <button class="btn btn-secondary" onclick="showBulkInvoiceModal()">
                                    <i class="fas fa-layer-group"></i> Hromadné fakturovanie
                                </button>
                            </div>
                        </div>

                        <!-- Invoices Stats -->
                        <div class="invoices-stats">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-file-invoice"></i></div>
                                <div class="stat-content">
                                    <h3 id="totalInvoices">0</h3>
                                    <p>Vydané faktúry</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                                <div class="stat-content">
                                    <h3 id="paidInvoices">0</h3>
                                    <p>Uhradené (93%)</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                                <div class="stat-content">
                                    <h3 id="pendingInvoicesCount">0</h3>
                                    <p>Čakajúce</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-euro-sign"></i></div>
                                <div class="stat-content">
                                    <h3 id="totalInvoiceAmount">0,00 €</h3>
                                    <p>Celková suma</p>
                                </div>
                            </div>
                        </div>

                        <!-- Invoices Filters -->
                        <div class="invoices-filters">
                            <div class="filter-group">
                                <label>Filter:</label>
                                <select id="invoiceStatusFilter">
                                    <option value="all">Všetky</option>
                                    <option value="pending">Čakajúce</option>
                                    <option value="paid">Uhradené</option>
                                    <option value="overdue">Po splatnosti</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>Rok:</label>
                                <select id="invoiceYearFilter">
                                    <option value="2025">2025</option>
                                    <option value="2024">2024</option>
                                    <option value="all">Všetky</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>Hľadať:</label>
                                <input type="text" id="invoiceSearch" placeholder="Číslo faktúry, zákazník...">
                            </div>
                        </div>

                        <!-- Invoices List -->
                        <div class="invoices-list" id="invoicesList">
                            <!-- Invoices will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- SERVICES TAB -->
                <div id="services-tab" class="tab-pane">
                    <div class="services-layout">
                        <!-- Services Header -->
                        <div class="services-header">
                            <h2><i class="fas fa-cogs"></i> Správa služieb</h2>
                            <div class="services-actions">
                                <button class="btn btn-primary" onclick="showAddServiceModal()">
                                    <i class="fas fa-plus"></i> Nová služba
                                </button>
                                <button class="btn btn-secondary" onclick="resetServicePrices()">
                                    <i class="fas fa-redo"></i> Obnoviť predvolené ceny
                                </button>
                            </div>
                        </div>

                        <!-- Services Categories -->
                        <div class="services-categories">
                            <div class="service-category-section">
                                <h3><i class="fas fa-tools"></i> Základné služby</h3>
                                <div class="services-grid" id="basicServicesGrid">
                                    <!-- Basic services will be populated by JavaScript -->
                                </div>
                            </div>

                            <div class="service-category-section">
                                <h3><i class="fas fa-box"></i> Balíky služieb</h3>
                                <div class="services-grid" id="packageServicesGrid">
                                    <!-- Package services will be populated by JavaScript -->
                                </div>
                            </div>

                            <div class="service-category-section">
                                <h3><i class="fas fa-qrcode"></i> Digitálne služby</h3>
                                <div class="services-grid" id="digitalServicesGrid">
                                    <!-- Digital services will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SETTINGS TAB -->
                <div id="settings-tab" class="tab-pane">
                    <div class="settings-layout">
                        <h2><i class="fas fa-cog"></i> Nastavenia</h2>
                        <div class="settings-content">
                            <div class="settings-section">
                                <h3>Všeobecné nastavenia</h3>
                                <div class="setting-item">
                                    <label>Predvolená lokalita:</label>
                                    <select id="defaultLocation">
                                        <option value="bratislava">Bratislava</option>
                                        <option value="petrzalka">Petržalka</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>Automatické plánovanie úloh:</label>
                                    <input type="checkbox" id="autoScheduling" checked>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>CRM a fakturácia</h3>
                                <div class="setting-item">
                                    <label>Automatické číslovanie faktúr:</label>
                                    <input type="checkbox" id="autoInvoiceNumbering" checked>
                                </div>
                                <div class="setting-item">
                                    <label>Predvolená splatnosť faktúr (dni):</label>
                                    <input type="number" id="defaultDueDays" value="2" min="1" max="30">
                                </div>
                                <div class="setting-item">
                                    <label>DPH sadzba (%):</label>
                                    <input type="number" id="vatRate" value="20" min="0" max="100" step="0.1">
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Firemné údaje</h3>
                                <div class="setting-item">
                                    <label>Telefón:</label>
                                    <input type="tel" id="companyPhone" value="+421 xxx xxx xxx">
                                </div>
                                <div class="setting-item">
                                    <label>Email:</label>
                                    <input type="email" id="companyEmail" value="<EMAIL>">
                                </div>
                                <div class="setting-item">
                                    <label>Webstránka:</label>
                                    <input type="url" id="companyWebsite" value="www.animamundi.sk">
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Štatistiky a reporty</h3>
                                <div class="settings-actions">
                                    <button class="btn btn-primary" onclick="showAdvancedStatsModal()">
                                        <i class="fas fa-chart-bar"></i> Pokročilé štatistiky
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportReport('monthly')">
                                        <i class="fas fa-download"></i> Export mesačný report
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportReport('clients')">
                                        <i class="fas fa-download"></i> Export klienti
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportReport('invoices')">
                                        <i class="fas fa-download"></i> Export faktúry
                                    </button>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Testovanie funkcií</h3>
                                <div class="settings-actions">
                                    <button class="btn btn-primary" onclick="testPDFGeneration()">
                                        <i class="fas fa-file-pdf"></i> Test PDF generovania
                                    </button>
                                    <button class="btn btn-secondary" onclick="createSampleData()">
                                        <i class="fas fa-database"></i> Vytvoriť vzorové dáta
                                    </button>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Údržba systému</h3>
                                <div class="settings-actions">
                                    <button class="btn btn-warning" onclick="clearAllData()">
                                        <i class="fas fa-trash"></i> Vymazať všetky dáta
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportAllData()">
                                        <i class="fas fa-download"></i> Zálohovať dáta
                                    </button>
                                    <button class="btn btn-secondary" onclick="importData()">
                                        <i class="fas fa-upload"></i> Obnoviť dáta
                                    </button>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Notifikácie</h3>
                                <div class="setting-item">
                                    <label>Pripomienky úloh:</label>
                                    <input type="checkbox" id="taskReminders" checked>
                                </div>
                                <div class="setting-item">
                                    <label>Email notifikácie:</label>
                                    <input type="checkbox" id="emailNotifications">
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Správa dát</h3>
                                <div class="setting-item">
                                    <label>Ukážkové dáta:</label>
                                    <button class="btn btn-secondary" onclick="resetToSampleData()">
                                        <i class="fas fa-redo"></i> Obnoviť ukážkové dáta
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <label>Vymazať všetko:</label>
                                    <button class="btn btn-danger" onclick="clearAllData()">
                                        <i class="fas fa-trash"></i> Vymazať všetky dáta
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <label>Export dát:</label>
                                    <button class="btn btn-primary" onclick="exportData()">
                                        <i class="fas fa-download"></i> Exportovať dáta
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modals -->
        <!-- Add Order Modal -->
        <div id="addOrderModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-plus"></i> Pridať objednávku</h3>
                    <span class="close" onclick="closeModal('addOrderModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addOrderForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Meno zákazníka:</label>
                                <input type="text" id="orderCustomerName" required>
                            </div>
                            <div class="form-group">
                                <label>Telefón:</label>
                                <input type="tel" id="orderCustomerPhone" required>
                            </div>
                            <div class="form-group">
                                <label>Email:</label>
                                <input type="email" id="orderCustomerEmail" required>
                            </div>
                            <div class="form-group">
                                <label>Lokalita:</label>
                                <select id="orderLocation" required>
                                    <option value="">Vyberte lokalitu</option>
                                    <option value="bratislava">Cintorín Bratislava</option>
                                    <option value="petrzalka">Cintorín Petržalka</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Typ balíka:</label>
                                <select id="orderPackage" required>
                                    <option value="">Vyberte balík</option>
                                    <option value="sviatocny_urnove">Sviatočný - Urnové miesto</option>
                                    <option value="sviatocny_jednohrob">Sviatočný - Jednohrob</option>
                                    <option value="sviatocny_dvojhrob">Sviatočný - Dvojhrob</option>
                                    <option value="celorocny_urnove">Celoročný Premium - Urnové miesto</option>
                                    <option value="celorocny_jednohrob">Celoročný Premium - Jednohrob</option>
                                    <option value="celorocny_dvojhrob">Celoročný Premium - Dvojhrob</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Dátum začiatku:</label>
                                <input type="date" id="orderStartDate" required>
                            </div>
                            <div class="form-group">
                                <label>Zostávajúce čistenia (voliteľné):</label>
                                <input type="number" id="orderRemainingCleanings" min="0" max="100" placeholder="Automaticky podľa balíka">
                                <small class="form-help">Nechajte prázdne pre automatický výpočet podľa balíka. Použite pre migráciu klientov z iného systému.</small>
                            </div>
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addOrderModal')">Zrušiť</button>
                            <button type="submit" class="btn btn-primary">Vytvoriť objednávku</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Calendar Modal -->
        <div id="calendarModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3><i class="fas fa-calendar"></i> Kalendárny pohľad</h3>
                    <span class="close" onclick="closeModal('calendarModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="calendar-container">
                        <div class="calendar-header">
                            <button class="btn btn-secondary" onclick="previousMonth()">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <h4 id="calendarTitle">Marec 2025</h4>
                            <button class="btn btn-secondary" onclick="nextMonth()">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div id="calendarGrid" class="calendar-grid">
                            <!-- Calendar will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Detail Modal -->
        <div id="taskDetailModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-info-circle"></i> Detail úlohy</h3>
                    <span class="close" onclick="closeModal('taskDetailModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="taskDetailContent">
                        <!-- Task details will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Progress Modal -->
        <div id="taskProgressModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3><i class="fas fa-tasks"></i> Priebeh úlohy</h3>
                    <span class="close" onclick="closeModal('taskProgressModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="taskProgressContent">
                        <!-- Task progress will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Completion Modal -->
        <div id="taskCompletionModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-check-circle"></i> Úloha dokončená</h3>
                    <span class="close" onclick="closeModal('taskCompletionModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="taskCompletionContent">
                        <!-- Completion summary will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Photo Viewer Modal -->
        <div id="photoViewerModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3><i class="fas fa-images"></i> Fotografie</h3>
                    <span class="close" onclick="closeModal('photoViewerModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="photoViewerContent">
                        <!-- Photos will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders List Modal -->
        <div id="ordersListModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3><i class="fas fa-list"></i> Správa objednávok</h3>
                    <span class="close" onclick="closeModal('ordersListModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="orders-list-container">
                        <div class="orders-list-header">
                            <div class="orders-search">
                                <input type="text" id="ordersSearch" placeholder="Hľadať objednávky...">
                                <button class="btn btn-primary" onclick="showAddOrderModal(); closeModal('ordersListModal')">
                                    <i class="fas fa-plus"></i> Pridať novú
                                </button>
                            </div>
                        </div>
                        <div id="ordersListContent" class="orders-list-content">
                            <!-- Orders will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Order Modal -->
        <div id="editOrderModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-edit"></i> Upraviť objednávku</h3>
                    <span class="close" onclick="closeModal('editOrderModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="editOrderForm">
                        <input type="hidden" id="editOrderId">
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Meno zákazníka:</label>
                                <input type="text" id="editOrderCustomerName" required>
                            </div>
                            <div class="form-group">
                                <label>Telefón:</label>
                                <input type="tel" id="editOrderCustomerPhone" required>
                            </div>
                            <div class="form-group">
                                <label>Email:</label>
                                <input type="email" id="editOrderCustomerEmail" required>
                            </div>
                            <div class="form-group">
                                <label>Lokalita:</label>
                                <select id="editOrderLocation" required>
                                    <option value="">Vyberte lokalitu</option>
                                    <option value="bratislava">Cintorín Bratislava</option>
                                    <option value="petrzalka">Cintorín Petržalka</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Balík služieb:</label>
                                <select id="editOrderPackage" required>
                                    <option value="">Vyberte balík</option>
                                    <option value="zakladny_urnove">Základný - Urnové miesto</option>
                                    <option value="zakladny_jednohrob">Základný - Jednohrob</option>
                                    <option value="zakladny_dvojhrob">Základný - Dvojhrob</option>
                                    <option value="celorocny_urnove">Celoročný Premium - Urnové miesto</option>
                                    <option value="celorocny_jednohrob">Celoročný Premium - Jednohrob</option>
                                    <option value="celorocny_dvojhrob">Celoročný Premium - Dvojhrob</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Dátum začiatku:</label>
                                <input type="date" id="editOrderStartDate" required>
                            </div>
                            <div class="form-group">
                                <label>Zostávajúce čistenia:</label>
                                <input type="number" id="editOrderRemainingCleanings" min="0" max="100" required>
                                <small class="form-help">Aktuálny počet zostávajúcich čistení pre túto objednávku.</small>
                            </div>
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('editOrderModal')">Zrušiť</button>
                            <button type="submit" class="btn btn-primary">Uložiť zmeny</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- CRM MODALS -->

        <!-- Add Contact Modal -->
        <div id="addContactModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-user-plus"></i> Pridať nový kontakt</h3>
                    <span class="close" onclick="closeModal('addContactModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addContactForm" onsubmit="handleAddContact(event)">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="contactName">Meno a priezvisko *</label>
                                <input type="text" id="contactName" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="contactEmail">Email *</label>
                                <input type="email" id="contactEmail" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="contactPhone">Telefón</label>
                                <input type="tel" id="contactPhone" name="phone">
                            </div>
                            <div class="form-group">
                                <label for="contactCompany">Spoločnosť</label>
                                <input type="text" id="contactCompany" name="company">
                            </div>
                            <div class="form-group">
                                <label for="contactStatus">Status</label>
                                <select id="contactStatus" name="status">
                                    <option value="lead">Lead</option>
                                    <option value="prospect">Prospect</option>
                                    <option value="customer">Zákazník</option>
                                    <option value="inactive">Neaktívny</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="contactDealValue">Hodnota obchodu (€)</label>
                                <input type="number" id="contactDealValue" name="dealValue" min="0" step="0.01">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="contactNotes">Poznámky</label>
                            <textarea id="contactNotes" name="notes" rows="3"></textarea>
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addContactModal')">
                                Zrušiť
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Uložiť kontakt
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Add Task Modal -->
        <div id="addTaskModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-plus"></i> Pridať novú úlohu</h3>
                    <span class="close" onclick="closeModal('addTaskModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm" onsubmit="handleAddTask(event)">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="taskTitle">Názov úlohy *</label>
                                <input type="text" id="taskTitle" name="title" required>
                            </div>
                            <div class="form-group">
                                <label for="taskPriority">Priorita</label>
                                <select id="taskPriority" name="priority">
                                    <option value="low">Nízka</option>
                                    <option value="medium" selected>Stredná</option>
                                    <option value="high">Vysoká</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="taskStatus">Status</label>
                                <select id="taskStatus" name="status">
                                    <option value="new" selected>Nové</option>
                                    <option value="in_progress">V riešení</option>
                                    <option value="review">Na kontrole</option>
                                    <option value="done">Dokončené</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="taskAssignee">Pridelený užívateľ</label>
                                <input type="text" id="taskAssignee" name="assignee">
                            </div>
                            <div class="form-group">
                                <label for="taskDeadline">Termín</label>
                                <input type="date" id="taskDeadline" name="deadline">
                            </div>
                            <div class="form-group">
                                <label for="taskContact">Súvisiaci kontakt</label>
                                <select id="taskContact" name="contactId">
                                    <option value="">Vyberte kontakt...</option>
                                    <!-- Populated by JavaScript -->
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="taskDescription">Popis úlohy</label>
                            <textarea id="taskDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="taskLabels">Značky (oddelené čiarkou)</label>
                            <input type="text" id="taskLabels" name="labels" placeholder="urgent, cleaning, follow-up">
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addTaskModal')">
                                Zrušiť
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Vytvoriť úlohu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Add Deal Modal -->
        <div id="addDealModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-chart-line"></i> Pridať novú príležitosť</h3>
                    <span class="close" onclick="closeModal('addDealModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addDealForm" onsubmit="handleAddDeal(event)">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="dealTitle">Názov príležitosti *</label>
                                <input type="text" id="dealTitle" name="title" required>
                            </div>
                            <div class="form-group">
                                <label for="dealValue">Hodnota (€) *</label>
                                <input type="number" id="dealValue" name="value" required min="0" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="dealStage">Štádium</label>
                                <select id="dealStage" name="stage">
                                    <option value="lead" selected>Lead</option>
                                    <option value="qualified">Kvalifikovaný</option>
                                    <option value="presentation">Prezentácia</option>
                                    <option value="proposal">Návrh</option>
                                    <option value="negotiation">Rokovanie</option>
                                    <option value="closed">Zatvorené</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="dealProbability">Pravdepodobnosť (%)</label>
                                <input type="number" id="dealProbability" name="probability" min="0" max="100" value="50">
                            </div>
                            <div class="form-group">
                                <label for="dealCompany">Spoločnosť</label>
                                <input type="text" id="dealCompany" name="company">
                            </div>
                            <div class="form-group">
                                <label for="dealCloseDate">Očakávaný termín zatvorenia</label>
                                <input type="date" id="dealCloseDate" name="expectedCloseDate">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="dealDescription">Popis</label>
                            <textarea id="dealDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addDealModal')">
                                Zrušiť
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Vytvoriť príležitosť
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="orders.js"></script>
    <script src="crm-invoicing.js"></script>
    <script src="crm-system.js"></script>
</body>
</html>
